<template>
  <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
    <slot v-if="hasDivider" name="divider">
      <el-divider>
        <el-icon>
          <CaretBottom />
        </el-icon>
        <span v-text="$t('dialog.timeSetting')" />
      </el-divider>
    </slot>
    <el-col :span="8">
      <el-form-item :label="$t('dialog.timeZoneHours') + ':'" prop="timeZoneHour">
        <bf-input-number-v2 v-model="timeZone.timeZoneHour" step-strictly :min="-12" :max="12" :step="1" class="!w-full h-[50px]" />
      </el-form-item>
    </el-col>
    <el-col :span="8">
      <el-form-item :label="$t('dialog.timeZoneMinutes') + ':'" prop="timeZoneMinute">
        <bf-input-number-v2 v-model="timeZone.timeZoneMinute" step-strictly :min="0" :max="59" :step="1" class="!w-full h-[50px]" />
      </el-form-item>
    </el-col>
    <el-col :span="8">
      <el-form-item :label="$t('dialog.deviceTime') + ':'">
        <span v-text="deviceTimeString" />
        <el-tooltip popper-class="bf-tooltip" :content="$t('writeFreq.synchronisedTime')" :open-delay="1000" placement="top">
          <bf-button circle color-type="primary" icon="refresh" size="small" @click="syncLocalTime" />
        </el-tooltip>
      </el-form-item>
    </el-col>
  </el-row>
</template>

<script>
  import dayjs from 'dayjs'
  import { DayMask } from '@/utils/time'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import bfButton from '@/components/bfButton/main'

  function getLocalDateNumbers(date = new Date()) {
    return {
      second: date.getSeconds(),
      minute: date.getMinutes(),
      hour: date.getHours(),
      day: date.getDate(),
      month: date.getMonth(),
      year: date.getFullYear(),
    }
  }

  function getTimeZoneOffsetObject(TimeZoneOffset = new Date().getTimezoneOffset()) {
    return {
      // 北京时区为正8时区，与utc时间相差-480分钟，因为显示为正8时区，所以乘-1转换为正数
      timeZoneHour: ((TimeZoneOffset * -1) / 60) | 0,
      timeZoneMinute: Math.abs(TimeZoneOffset) % 60,
    }
  }

  export default {
    name: 'TimeZoneV2',
    components: {
      bfInputNumberV2,
      bfButton,
    },
    emits: ['update:modelValue'],
    props: {
      hasDivider: {
        type: Boolean,
        default: true,
      },
      modelValue: {
        type: Object,
      },
    },
    data() {
      const timeZone = {
        // 时区小时和分钟
        timeZoneHour: 0,
        timeZoneMinute: 0,
        // 本地时间的年、月、日和时、分、秒
        second: 0,
        minute: 0,
        hour: 0,
        day: 0,
        month: 0,
        year: 0,
      }
      Object.assign(timeZone, getLocalDateNumbers())
      Object.assign(timeZone, getTimeZoneOffsetObject())

      return {
        timeZone,
        timer: null,
      }
    },
    methods: {
      syncLocalTime() {
        Object.assign(this.timeZone, getLocalDateNumbers())
        Object.assign(this.timeZone, getTimeZoneOffsetObject())
      },
      getLocalDate() {
        return new Date(this.timeZone.year, this.timeZone.month, this.timeZone.day, this.timeZone.hour, this.timeZone.minute, this.timeZone.second)
      },
      emitTimeZone() {
        // 向父组件发送"input"事件，以同步v-model参数
        this.$emit('update:modelValue', this.timeZone)
      },
      startTimeZoneInterval() {
        this.clearTimeZoneInterval()
        this.timer = setInterval(() => {
          this.timeZone.second += 1
          Object.assign(this.timeZone, getLocalDateNumbers(this.getLocalDate()))
          this.emitTimeZone()
        }, 1000)
      },
      clearTimeZoneInterval() {
        if (this.timer !== null) {
          clearInterval(this.timer)
          this.timer = null
        }
      },
      // 初始化时区参数，父组件需要重置时会通过ref调用
      initTimeZone() {
        this.syncLocalTime()
        this.emitTimeZone()
        this.startTimeZoneInterval()
      },
    },
    computed: {
      deviceTimeString() {
        const date = dayjs(this.getLocalDate()).utc().add(this.timeZone.timeZoneHour, 'hour').add(this.timeZone.timeZoneMinute, 'minute')

        return date.format(DayMask)
      },
    },
    watch: {
      modelValue: {
        handler(data) {
          Object.assign(this.timeZone, data)
        },
        deep: true,
      },
    },
    beforeMount() {
      this.initTimeZone()
    },
    beforeUnmount() {
      this.clearTimeZoneInterval()
    },
  }
</script>

<style></style>
