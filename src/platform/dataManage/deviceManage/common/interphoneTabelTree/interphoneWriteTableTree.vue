<template>
  <div class="!w-[250px] tree-grid-container ![border-image:none] ![border-width:0]">
    <vxe-table
      ref="tableRef"
      v-bind="$attrs"
      :data="sourceData"
      :show-header="false"
      :show-footer="false"
      show-overflow="ellipsis"
      height="100%"
      border="none"
      :keep-source="true"
      :cell-config="vxeCellConfig"
      :row-config="vxeRowConfig"
      :tree-config="vxeTreeConfig"
      :virtual-y-config="{ enabled: true, gt: 0 }"
    >
      <vxe-column field="key" :tree-node="true">
        <template #default="{ row }">
          <div class="vxe-column-content-wrapper relative h-[20px] px-[5px] w-full inline-flex gap-[5px]">
            <div ref="contextValueRef" class="vxe-column-content relative w-auto px-[5px] cursor-default flex justify-start items-center max-w-full">
              <ellipsis-text :content="row.title" class="cursor-pointer"></ellipsis-text>
            </div>
          </div>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script lang="ts" setup>
  import type { VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
  import { VxeTable } from 'vxe-table'
  import { useTemplateRef, ref } from 'vue'
  import { TreeNodeData } from './types'
  import { calcScaleSize } from '@/utils/setRem'
  import { useResizeObserver } from '@vueuse/core'

  defineProps<{
    sourceData: TreeNodeData[]
  }>()

  const tableRef = useTemplateRef<VxeTableInstance<TreeNodeData>>('tableRef')
  const vxeRowConfig = ref<VxeTablePropTypes.RowConfig>({ useKey: true, keyField: 'key', isHover: true, isCurrent: true })
  const vxeTreeConfig = ref<VxeTablePropTypes.TreeConfig>({
    transform: true,
    rowField: 'key',
    parentField: 'parentKey',
    expandAll: true,
    padding: true,
    reserve: true,
    indent: calcScaleSize(12),
    iconClose: 'bf-iconfont bfdx-shouqi',
    iconOpen: 'bf-iconfont bfdx-zhankai',
    showLine: false,
  })

  const vxeCellConfig = ref<VxeTablePropTypes.CellConfig>({
    height: calcScaleSize(36),
  })

  useResizeObserver(document.body, () => {
    vxeTreeConfig.value.indent = calcScaleSize(12)
    vxeCellConfig.value.height = calcScaleSize(36)
    tableRef.value?.updateData()
  })

  const collapseAll = () => {
    tableRef.value?.clearTreeExpand()
  }

  const expandAll = () => {
    tableRef.value?.setAllTreeExpand(true)
  }

  // 通过rid滚动到对应的节点
  const scrollToRowByRid = (rid: string) => {
    const row = tableRef.value?.getRowById(rid)
    tableRef.value?.scrollToRow(row)
    tableRef.value?.setCurrentRow(row)
  }

  // 高亮节点
  const setCurrentRowByRid = (rid: string) => {
    const row = tableRef.value?.getRowById(rid)
    tableRef.value?.setCurrentRow(row)
  }

  defineExpose({
    collapseAll,
    expandAll,
    scrollToRowByRid,
    setCurrentRowByRid,
  })
</script>

<style lang="scss">
  @use '@/assets/bfdxFont/iconfont.css';
  [data-vxe-ui-theme='dark'] {
    --vxe-ui-font-family: 'AlibabaPuHuiTi2';
    --vxe-ui-font-primary-color: #7e8fa6;
    --vxe-ui-font-color: #fff;
    --vxe-ui-font-lighten-color: #1a7aff;
    --vxe-ui-font-size-default: 12px;
    --vxe-ui-table-row-height-default: 36px;
    --vxe-ui-table-cell-padding-default: 8px;
    --vxe-ui-table-row-line-height: 20px;
    --vxe-ui-layout-background-color: transparent;
    --vxe-ui-base-popup-border-color: transparent;
    --vxe-ui-table-menu-background-color: rgba(0, 63, 107, 0.8);
    --vxe-ui-table-menu-item-width: auto;
    --vxe-ui-table-row-hover-background-color: transparent;
    --vxe-ui-table-row-current-background-color: transparent;
    --vxe-ui-table-row-hover-current-background-color: transparent;
    --vxe-ui-table-row-checkbox-checked-background-color: transparent;
    --vxe-ui-table-row-hover-checkbox-checked-background-color: transparent;
  }

  .vxe-table--context-menu-wrapper {
    border: none;
    border-radius: 4px;
    box-shadow: none;
    .vxe-context-menu--link {
      max-width: 200px;
      padding: 0 5px;
      text-align: center;
      .vxe-context-menu--link-prefix,
      .vxe-context-menu--link-suffix {
        min-width: 0;
      }
    }
    .vxe-context-menu--option-wrapper,
    .vxe-table--context-menu-clild-wrapper {
      border: none;
      position: relative;
      &:not(:last-child)::after {
        content: '';
        position: absolute;
        display: block;
        height: 1px;
        left: 12px;
        right: 12px;
        background-color: #7e8fa6;
      }

      li {
        margin: 0;
        border: none;
      }
      li.link--active {
        background: transparent;
        border: none;

        .vxe-context-menu--link {
          color: #ff811d;
        }
      }
    }
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 4px;
      padding: 1px;
      background: linear-gradient(to bottom, rgba(20, 186, 255, 0.68) 0, rgba(20, 186, 255, 0.68) 78%, rgba(122, 136, 203, 0) 100%);
      mask:
        linear-gradient(#000 0 0) content-box,
        linear-gradient(#000 0 0);
      mask-composite: exclude;
      pointer-events: none;
    }
  }

  .tree-grid-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.22), transparent);
    border-width: 1px;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;
    height: 100%;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 4, 0.27);
      pointer-events: none;
      filter: blur(200px);
    }
  }
  .vxe-table.vxe-table--render-default {
    width: 100%;
    .vxe-tree--line {
      border-bottom-style: dotted;
      border-left-style: solid;
    }
    .vxe-body--row.row--current > .vxe-body--column .vxe-tree-cell .el-tooltip__trigger {
      background-color: #00355b;
      border-radius: 5px;
    }
    .vxe-body--row.row--hover > .vxe-body--column .vxe-tree-cell .el-tooltip__trigger {
      background-color: rgba(0, 53, 91, 0.5);
      border-radius: 5px;
    }
  }
  .vxe-cell--tree-node {
    height: 20px;
    .vxe-cell--tree-btn {
      width: 20px;
      top: 0;
      transform: none;
      i {
        font-size: 20px;
        color: #6b7078;
      }
    }
    &.is--active .vxe-cell--tree-btn i {
      color: #1a7aff;
    }
  }

  /** 设置连接线开始 */
  // 确保连接线显示完整
  .vxe-table--render-default .vxe-header--column:not(.col--active).col--ellipsis > .vxe-cell,
  .vxe-table--render-default .vxe-body--column:not(.col--active).col--ellipsis > .vxe-cell,
  .vxe-table--render-default .vxe-footer--column:not(.col--active).col--ellipsis > .vxe-cell {
    overflow: visible;
    & > .vxe-cell--wrapper {
      overflow: visible;
    }
  }

  .vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-row-group-cell,
  .vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
    overflow: visible;
  }

  [class*='row--level-']:not(.row--level-0):not(:has(.vxe-cell--tree-btn)) {
    .vxe-column-content-wrapper::before {
      content: '';
      position: absolute;
      height: 36px;
      width: 24px;
      left: -20px;
      bottom: 10px;
      border-left: 1px solid rgba(126, 143, 166, 0.5);
      border-bottom: 1px dotted rgba(126, 143, 166, 0.5);
    }

    &:has(.vxe-column-checkbox) .vxe-column-content-wrapper::before {
      left: -21px;
    }
  }

  // 降低第一个节点的连接线高度
  [class*='row--level-']:has(.vxe-cell--tree-btn) + [class*='row--level-'] {
    .vxe-column-content-wrapper::before {
      height: 24px;
    }
  }
</style>
