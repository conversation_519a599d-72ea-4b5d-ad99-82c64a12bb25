<template>
  <el-form :model="info" :label-width="labelWidth" label-position="top" class="grid grid-cols-1 gap-3 deviceWriteInfo-form">
    <el-form-item :label="$t('dialog.model') + ':'">
      <bfInput :value="modelName" readonly />
    </el-form-item>
    <el-form-item :label="$t('dialog.frequencyRange') + ':'">
      <bfInput :value="frequencyRange" readonly />
    </el-form-item>
    <el-form-item :label="$t('dialog.serialNumber') + ':'">
      <bfInput v-model="info.serizeNumber" readonly />
    </el-form-item>
    <el-form-item :label="$t('dialog.firmwareVersion') + ':'">
      <bfInput v-model="firmwareVersion" readonly />
    </el-form-item>
    <el-form-item v-if="hasUiVersion" :label="$t('writeFreq.uiVersion') + ':'">
      <bfInput v-model="info.uiVersion" readonly />
    </el-form-item>

    <slot v-if="$slots['resourceVersion']" name="resourceVersion" />

    <slot v-if="showOptFeatures" name="features">
      <div class="grid grid-cols-1 gap-2 option-features-container">
        <el-form-item v-if="hasOptsFunType" :label="$t('writeFreq.optionalFeatures') + ':'">
          <el-radio-group v-model="localOptsFunType" @change="onOptsFunTypeChange">
            <bfRadio :value="0">
              {{ $t('writeFreq.recordingOption') }}
            </bfRadio>
            <bfRadio :value="1">
              {{ $t('writeFreq.bluetoothOption') }}
            </bfRadio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <bfCheckbox v-model="info.config.locate" disabled>
            <span v-text="$t('writeFreq.locateFunc')" />
          </bfCheckbox>
        </el-form-item>
        <el-form-item v-if="localOptsFunType === 0">
          <bfCheckbox v-model="info.config.recording" disabled>
            <span v-text="$t('writeFreq.recordingFunc')" />
          </bfCheckbox>
        </el-form-item>
        <el-form-item v-if="localOptsFunType === 1">
          <bfCheckbox v-model="info.config.bluetooth" disabled>
            <span v-text="$t('writeFreq.bluetoothFunc')" />
          </bfCheckbox>
        </el-form-item>
        <el-form-item>
          <bfCheckbox v-model="info.config.runBackward" disabled>
            <span v-text="$t('writeFreq.runBackwardAlarm')" />
          </bfCheckbox>
        </el-form-item>
        <el-form-item>
          <bfCheckbox v-model="info.config.workAlone" disabled>
            <span v-text="$t('writeFreq.workAloneAlarm')" />
          </bfCheckbox>
        </el-form-item>
        <el-form-item>
          <bfCheckbox v-model="info.config.roaming" disabled>
            <span v-text="$t('writeFreq.roaming')" />
          </bfCheckbox>
        </el-form-item>
      </div>
    </slot>
  </el-form>
</template>

<script>
  import { cloneDeep } from 'lodash'
  import { getModelName } from '@/writingFrequency/modelInfo'
  import RepeaterWfMixin from '@/utils/repeaterWfMixin'
  import { getDeviceModelName } from '@/writingFrequency/customModelConfig'
  // import * as models from '@/writingFrequency/interphone/models'
  import { checkModel } from '@/writingFrequency/interphone/common'
  import { SupportedLang } from '@/modules/i18n'
  import bfInput from '@/components/bfInput/main'
  import bfCheckbox from '@/components/bfCheckbox/main'
  import bfRadio from '@/components/bfRadio/main'

  const defaultMinFreq = 400
  const defaultMaxFreq = 480

  const DeviceConfig = {
    locate: true,
    recording: true,
    runBackward: true,
    workAlone: true,
    roaming: true,
    bluetooth: false,
  }
  const DeviceWriteInfo = {
    serizeNumber: '',
    firmwareVersion: '',
    minFrequency: defaultMinFreq,
    maxFrequency: defaultMaxFreq,
    model: '',
    uiVersion: '',
  }
  const oneFreqRange = {
    min: defaultMinFreq,
    max: defaultMaxFreq,
  }
  const multipleFreqDeviceInfo = {
    model: '',
    frequencyRange: [],
    firmwareVersion: '',
  }

  // 旧版本机型均显示3段版本，即x.y.z，主要为SDC类型
  // 2022年替换为R7F芯片后，均显示4段版本号，x.y.z.a，
  // a为新增的第四位是编译版本号，服务器编译会自动递增的，以后均默认为4段版本号
  const version3FieldModels = [
    // TD800(SDC)
    'TD081000',
    // TD510(SDC)
    '510SDC00',
    // TD511(SDC)
    '511SDC00',
    // TD910(SDC)
    'DP109SDC',
    // TD910P(SDC)
    '109PSDC1',
    // TD880(SDC)
    '880SC100',
  ]
  // 非字节类型的机型码，以对象的形式存储
  // TM825, TD930, TD930SVT等福州研发的机型
  // const objectVersionModels = [models.TM825SDCModel, models.TD930SDCModel, models.TD930SVTModel]

  export default {
    name: 'DeviceInfo',
    components: {
      bfInput,
      bfCheckbox,
      bfRadio,
    },
    mixins: [RepeaterWfMixin],
    emits: ['update:modelValue', 'update:optsFunType'],
    props: {
      modelValue: {
        type: Object,
        required: true,
      },
      model: {
        type: String,
        required: true,
      },
      showOptFeatures: {
        type: Boolean,
        default: false,
      },
      multipleFreqRange: {
        type: Number,
        default: 0,
      },
      hasUiVersion: {
        type: Boolean,
        default: false,
      },
      // 是否拥有选配功能类型
      hasOptsFunType: {
        type: Boolean,
        default: false,
      },
      // 选配功能类型
      // 0：录音选配，1：蓝牙选配
      optsFunType: {
        type: Number,
        default: 0,
      },
    },
    data() {
      let deviceInfo = cloneDeep(DeviceWriteInfo)
      if (this.multipleFreqRange) {
        deviceInfo = cloneDeep(multipleFreqDeviceInfo)
        const frequencyRange = []
        let i = 0
        while (i < this.multipleFreqRange) {
          frequencyRange.push(cloneDeep(oneFreqRange))
          i++
        }
        deviceInfo.frequencyRange = frequencyRange
      }
      if (this.showOptFeatures) {
        Object.assign(deviceInfo, {
          config: deviceInfo.config ?? cloneDeep(DeviceConfig),
        })
      }

      if (this.model === '825SDC00') {
        Object.assign(deviceInfo, {
          maxFrequency: 470,
          minFrequency: 400,
        })
      }

      return {
        info: deviceInfo,
      }
    },
    methods: {
      onOptsFunTypeChange() {
        if (this.localOptsFunType === 0) {
          this.info.config.bluetooth = false
          this.info.config.recording = true
        } else if (this.localOptsFunType === 1) {
          this.info.config.bluetooth = true
          this.info.config.recording = false
        }
      },
    },
    watch: {
      modelValue: {
        deep: true,
        immediate: true,
        handler(val) {
          Object.assign(this.info, val)
          if (Object.keys(val).length === 0) {
            this.$emit('update:modelValue', this.info)
          }
        },
      },
      info: {
        deep: true,
        handler(val) {
          this.$nextTick(() => {
            this.$emit('update:modelValue', val)
          })
        },
      },
    },
    computed: {
      localOptsFunType: {
        get() {
          return this.optsFunType
        },
        set(value) {
          this.$emit('update:optsFunType', value)
        },
      },
      frequencyRange() {
        let frequencyRange = []
        if (this.multipleFreqRange) {
          frequencyRange = this.info.frequencyRange
            .map(item => {
              if (!item.min || !item.max) {
                return ''
              }
              return [item.min, item.max].join('-')
            })
            .filter(item => Boolean(item))
          return frequencyRange.join(',')
        } else {
          frequencyRange = [this.info.minFrequency, this.info.maxFrequency]
          if (!this.info.minFrequency || !this.info.maxFrequency) {
            frequencyRange = []
          }
          return frequencyRange.join('-')
        }
      },
      modelName() {
        return getDeviceModelName(this.info.model) || getModelName(this.info.model)
      },
      firmwareVersion() {
        const version = this.info.firmwareVersion
        // 兼容旧版本机型的x.y.z版本规则
        if (checkModel(version3FieldModels, this.info.model)) {
          return version.split('.').slice(0, 3).join('.')
        }

        // 默认为4段的x.y.z.a版本规则
        return version
      },
      isFR() {
        return this.$i18n.locale === SupportedLang.fr
      },
      isEN() {
        return this.$i18n.locale === SupportedLang.enUS
      },
      labelWidth() {
        return this.isFR ? '200px' : this.isEN ? '165px' : '120px'
      },
    },
  }
</script>

<style lang="scss">
  .deviceWriteInfo-form {
    width: 400px;
    overflow: hidden;

    .el-form-item {
      margin-bottom: 0;
    }

    .option-features-container {
      .el-radio {
        line-height: 1.5;
      }
    }
  }
</style>
