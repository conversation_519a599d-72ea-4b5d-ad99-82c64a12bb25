<template>
  <div>
    <el-tabs v-model="writerFrequencyTabName" class="bf-tab interphone-tab" tab-position="left">
      <!--设备信息-->
      <el-tab-pane lazy :label="$t('dialog.deviceInfo')" name="deviceWriteInfo" class="settings-box">
        <deviceInfo ref="deviceWriteInfo" v-model="deviceWriteInfo" showOptFeatures :model="Model">
          <template #features>
            <div class="grid grid-cols-1 features option-features-container">
              <el-form-item>
                <bfCheckbox v-model="deviceWriteInfo.config.locate" disabled>
                  <span v-text="$t('writeFreq.locateFunc')" />
                </bfCheckbox>
              </el-form-item>
              <el-form-item>
                <bfCheckbox v-model="deviceWriteInfo.config.recording" disabled>
                  <span v-text="$t('writeFreq.recordingFunc')" />
                </bfCheckbox>
              </el-form-item>
              <el-form-item>
                <bfCheckbox v-model="deviceWriteInfo.config.sdc" disabled>
                  <span>SDC</span>
                </bfCheckbox>
              </el-form-item>
              <el-form-item>
                <bfCheckbox v-model="deviceWriteInfo.config.svt" disabled>
                  <span>SVT</span>
                </bfCheckbox>
              </el-form-item>
              <el-form-item>
                <bfCheckbox v-model="deviceWriteInfo.config.bluetooth" disabled>
                  <span v-text="$t('writeFreq.bluetoothFunc')" />
                </bfCheckbox>
              </el-form-item>
              <!-- <el-form-item>
                <el-checkbox
                  v-model="deviceWriteInfo.config.upendAlarm"
                  disabled
                >
                  <span v-text="$t('writeFreq.runBackwardAlarm')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox
                  v-model="deviceWriteInfo.config.workAlone"
                  disabled
                >
                  <span v-text="$t('writeFreq.workAloneAlarm')" />
                </el-checkbox>
              </el-form-item> -->
              <!-- <el-form-item>
                <el-checkbox
                  v-model="deviceWriteInfo.config.roam"
                  disabled
                >
                  <span v-text="$t('writeFreq.roaming')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox
                  v-model="deviceWriteInfo.config.denoise"
                  disabled
                >
                  <span v-text="$t('writeFreq.denoise')" />
                </el-checkbox>
              </el-form-item> -->
            </div>
          </template>
        </deviceInfo>
      </el-tab-pane>
      <!--常规设置-->
      <el-tab-pane lazy :label="$t('dialog.generalSetting')" name="generalSettings" class="general-settings-box settings-box">
        <el-form
          ref="generalSettings"
          class="general-settings-form"
          :model="generalSettings"
          label-width="95px"
          label-position="top"
          :rules="generalSettingsRules"
        >
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-col :span="8">
              <el-form-item :label="$t('dialog.terminalName') + ':'" prop="deviceName">
                <bfInput v-model="generalSettings.deviceName" :maxlength="16" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.terminalAlias') + ':'" prop="alias">
                <bfInput v-model="generalSettings.alias" :maxlength="16" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="DMRID:">
                <bfInput :value="dmrIdLabel" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.repeaterId') + ':'" prop="repeaterId">
                <bfInput v-model.number="generalSettings.repeaterId" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.programmingPwd') + ':'">
                <bfInput v-model="passwordInfo.md5Key" type="password" :maxlength="8" @input="fixMd5KeyValue" />
              </el-form-item>
            </el-col>
            <!--            <el-col :xs="24" :sm='12'>-->
            <!--              <el-form-item :label="$t('dialog.voiceCryptoType')" prop="soundEncryptType">-->
            <!--                <el-select v-model="generalSettings.soundEncryptType"-->
            <!--                           -->
            <!--                           :placeholder="$t('dialog.select')"-->
            <!--                           filterable-->
            <!--                           :no-match-text="$t('dialog.noMatchText')">-->
            <!--                  <el-option v-for="(item,i) in soundEncryptTypeList" :key="i"-->
            <!--                             :label="item.label"-->
            <!--                             :value="item.value"></el-option>-->
            <!--                </el-select>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <!--            <el-col :xs="24" :sm='12'>-->
            <!--              <el-form-item :label="$t('dialog.voiceCryptoKey')" prop="soundEncryptValue">-->
            <!--                <el-input v-model="generalSettings.soundEncryptValue"-->
            <!--                          type="password"-->
            <!--                          @input="fixSoundEncryptValue"-->
            <!--                          :maxlength="10"-->
            <!--                          :disabled='generalSettings.soundEncryptType===0'></el-input>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col v-if="!DeviceNoLocale" :span="8">
              <el-form-item :label="$t('dialog.languageType') + ':'" prop="locale">
                <bfSelect
                  v-model="generalSettings.locale"
                  class="!w-full !h-[50px]"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="item in localeList" :key="item.label" :label="item.label" :value="item.value" />
                </bfSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.voiceLevel') + ':'" prop="soundCtrlLevel">
                <bfInputNumberV2
                  v-model="generalSettings.soundCtrlLevel"
                  class="!w-full"
                  step-strictly
                  :min="0"
                  :max="8"
                  :step="1"
                  :formatter="
                    v => {
                      return v === 0 ? $t('writeFreq.off') : v
                    }
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.voiceDelay') + ':'" prop="soundCtrlDelay">
                <bfInputNumberV2 v-model="generalSettings.soundCtrlDelay" class="!w-full" step-strictly :min="500" :max="10000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.sendPreambleDuration') + ':'" prop="sendLeadCodeTime">
                <bfInputNumberV2 v-model="generalSettings.sendPreLastTime" class="!w-full" step-strictly :min="0" :max="8640" :step="240" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.offNetworkGroupCallHangTime') + ':'" prop="offlineGroupCallHungTime">
                <bfInputNumberV2 v-model="generalSettings.offlineGroupCallHungTime" class="!w-full" step-strictly :min="0" :max="7000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.offNetworkSingleCallHangTime') + ':'" prop="offlineSingleCallHungTime">
                <bfInputNumberV2 v-model="generalSettings.offlineSingleCallHungTime" class="!w-full" step-strictly :min="0" :max="7000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.powerSavingMode') + ':'" prop="savePowerMode">
                <bfSelect
                  v-model="generalSettings.savePowerMode"
                  class="!w-full !h-[50px]"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in savePowerModeList" :key="i" :label="item.label" :value="item.value" />
                </bfSelect>
              </el-form-item>
            </el-col>
            <!--            <el-col-->
            <!--                :xs="24"-->
            <!--                :sm="12"-->
            <!--            >-->
            <!--              <el-form-item-->
            <!--                  :label="$t('writeFreq.savePowerDelayTime')"-->
            <!--                  prop="savePowerDelayTime"-->
            <!--              >-->
            <!--                <el-input-number-->
            <!--                    v-model="generalSettings.savePowerDelayTime"-->
            <!--                    step-strictly-->
            <!--                    :min="5"-->
            <!--                    :max="60"-->
            <!--                    :step="1"-->
            <!--                ></el-input-number>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :span="8">
              <el-form-item prop="LEDConfig.disabledAllLED">
                <bfCheckbox v-model="generalSettings.LEDConfig.disabledAllLED">
                  <span v-text="$t('dialog.disabledAllLed')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="baseSettings.rejectUnfamiliarCall">
                <bfCheckbox v-model="generalSettings.baseSettings.rejectUnfamiliarCall">
                  <span v-text="$t('dialog.rejectingStrangeCalls')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="baseSettings.allowErasing">
                <bfCheckbox v-model="generalSettings.baseSettings.allowErasing">
                  <span v-text="$t('writeFreq.allowErasingDevice')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="baseSettings.allowAddUnfamiliar">
                <bfCheckbox v-model="generalSettings.baseSettings.allowAddUnfamiliar">
                  <span v-text="'自动添加陌生人到通讯录'" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>

          <!--定位模式-->
          <el-row v-if="isGConfig" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.locateMode')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.gpsSettings.gpsEnable">
                  <span v-text="$t('writeFreq.gpsLocate')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.gpsSettings.beiDouEnable">
                  <span v-text="$t('writeFreq.bdsLocate')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.gpsSettings.glonassEnable">
                  <span v-text="$t('writeFreq.glonass')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.gpsSettings.galileoEnable">
                  <span v-text="$t('writeFreq.galileo')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 声音提示 -->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.voicePrompt')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.soundAndDisplayTip.muteAll">
                  <span v-text="$t('dialog.muteAll')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.soundAndDisplayTip.voiceNotice" :disabled="muteAll">
                  <span v-text="$t('dialog.voiceIndication')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.soundAndDisplayTip.channelFreeNotice" :disabled="muteAll">
                  <span v-text="$t('dialog.channelIdleIndication')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.callPermissionIndication') + ':'">
                <bfSelect
                  v-model="generalSettings.soundAndDisplayTip.allowCallInstruction"
                  class="!w-full !h-[50px]"
                  :disabled="muteAll"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in allowCallInstructionList" :key="i" :label="item.label" :value="item.value" />
                </bfSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.receiveLowPowerPromptInterval') + ':'" prop="powerInfoAlert">
                <bfInputNumberV2 v-model="generalSettings.powerInfoAlert" class="!w-full" step-strictly :min="0" :max="635" :step="5" />
              </el-form-item>
            </el-col>
          </el-row>

          <!--密码-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('loginDlg.password')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.powerOnPwd') + ':'">
                <bfInput v-model="menuSettings.powerOnPwd" type="password" show-password :maxlength="6" :minlength="6" @input="fixPowerOnValue" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.channelConfigPassword') + ':'" class="select-input-form-item">
                <bfSelect
                  v-model="chConfigPwdMode"
                  class="!w-full !h-[50px]"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                  @change="val => passwordModeChange(menuSettings, 'chConfigPwd', val)"
                >
                  <el-option v-for="(item, i) in passwordModeList" :key="i" :label="item.label" :value="item.value" />
                </bfSelect>
                <bfInput
                  v-model="menuSettings.chConfigPwd"
                  type="password"
                  :disabled="chConfigPwdMode !== 1"
                  show-password
                  :maxlength="6"
                  :minlength="6"
                  @input="fixChConfigPwd"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!--隐蔽模式-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.stealthMode')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.stealthSettings.stealthModeEnable">
                  <span v-text="$t('writeFreq.stealthMode')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="generalSettings.stealthSettings.stealthModeHeadsetMute">
                  <span v-text="$t('writeFreq.stealthModeHeadsetMute')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>

          <!--蓝牙-->
          <el-row v-if="isBConfig" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.bluetooth')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="bluetoothSettings.enable">
                <bfCheckbox v-model="generalSettings.bluetoothSettings.enable">
                  <span v-text="$t('writeFreq.bluetoothSwitch')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="bluetoothSettings.pttKeepEnable">
                <bfCheckbox v-model="generalSettings.bluetoothSettings.pttKeepEnable" :disabled="!generalSettings.bluetoothSettings.enable">
                  <span v-text="$t('writeFreq.bluetoothPTTKeep')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>

          <!--录音-->
          <el-row v-if="isLConfig" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.recording')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="recordSettings.enable">
                <bfCheckbox v-model="generalSettings.recordSettings.enable">
                  <span v-text="$t('dialog.recordEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 降噪 -->
          <el-row v-if="isDConfig" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span>{{ $t('writeFreq.denoise') }}</span>
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="denoiseSettings.enable">
                <bfCheckbox v-model="generalSettings.denoiseSettings.enable">
                  <span>{{ $t('writeFreq.denoiseEnable') }}</span>
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <TimeZone v-if="isGConfig || isLConfig" ref="timeZone" v-model="timeZone" />
        </el-form>
      </el-tab-pane>
      <!--按键设置-->
      <el-tab-pane lazy :label="$t('dialog.buttonDefinition')" name="buttonDefine" class="buttonDefine-box settings-box">
        <el-form ref="buttonDefine" class="buttonDefine-form" :model="buttonDefined" label-position="top">
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]">
            <el-col :span="8">
              <el-form-item :label="$t('dialog.longPressDuration') + ':'" :label-width="buttonDefineLabelWidth">
                <bfInputNumberV2 v-model="buttonDefined.longPressTime" class="!w-full" step-strictly :min="250" :max="3750" :step="250" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table :data="sideKeyAndFrontKey" class="bf-table" :empty-text="$t('msgbox.emptyText')">
                <el-table-column label="" min-width="65">
                  <template #default="scope">
                    <span v-text="getKeyName(scope.$index)" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('dialog.shortPress')" min-width="100">
                  <template #default="scope">
                    <el-form-item label-width="0" class="!m-0">
                      <bfSelect
                        v-model="scope.row.short"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="
                          () => {
                            syncLongPressDefine(scope.row)
                          }
                        "
                      >
                        <el-option v-for="(shortKey, i) in getSoftKeyFuncDefine(0)" :key="i" :label="shortKey.label" :value="shortKey.value" />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('dialog.longPress')" min-width="85">
                  <template #default="scope">
                    <el-form-item label-width="0" class="!m-0">
                      <bfSelect
                        v-model="scope.row.long"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="
                          () => {
                            syncLongPressDefine(scope.row)
                          }
                        "
                      >
                        <el-option v-for="(shortKey, i) in getSoftKeyFuncDefine(1)" :key="i" :label="shortKey.label" :value="shortKey.value" />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
              <div class="flex justify-center items-center mt-2">
                <bfButton @click="showBtnDefinePreview">
                  {{ $t('writeFreq.imagePreview') }}
                </bfButton>
                <el-image ref="btnDefinePreviewRef" :src="btnDefinePreviewUrl" show-progress :preview-src-list="[btnDefinePreviewUrl]" fit="contain" hidden />
              </div>
            </el-col>
            <el-col :span="24">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.presetChannel')" />
              </el-divider>
              <el-table :data="buttonDefined.defaultChannel" class="bf-table" :empty-text="$t('msgbox.emptyText')">
                <el-table-column label="" min-width="100">
                  <template #default="scope">
                    <span v-text="$t(`writeFreq.defaultChannel.ch${scope.$index + 1}`)" />
                  </template>
                </el-table-column>
                <el-table-column min-width="100" :label="$t('writeFreq.zones.root')">
                  <template #default="scope">
                    <el-form-item label-width="0" class="!m-0">
                      <bfSelect
                        v-model="scope.row.areaId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="val => defaultChannelZoneRootIdChange(scope.row, val)"
                      >
                        <el-option v-for="item in buttonDefineZoneRootList" :key="item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column min-width="100" :label="$t('dialog.channel')">
                  <template #default="scope">
                    <el-form-item label-width="0" class="!m-0">
                      <bfSelect
                        v-model="scope.row.channelId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="item in buttonDefineChannelList[scope.row.areaId]" :key="item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="24">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.singleKeyFuncCall')" />
              </el-divider>
              <el-table :data="buttonDefined.singleKeyCall" class="bf-table" :empty-text="$t('msgbox.emptyText')">
                <el-table-column label="" type="index" />
                <el-table-column :label="$t('dialog.callTarget')" min-width="100">
                  <template #default="scope">
                    <el-form-item label-width="0" class="!m-0">
                      <bfSelect
                        v-model="scope.row.callId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="val => buttonDefinedCallIdChange(scope.row, val)"
                      >
                        <el-option v-for="item in buttonDefineAddressList" :key="item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('dialog.callType')" min-width="85">
                  <template #default="scope">
                    <el-form-item v-if="scope.row.callId !== 0xffff" label-width="0" class="!m-0">
                      <bfSelect
                        v-model="scope.row.callType"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="val => buttonDefinedCallTypeChange(scope.row, val)"
                      >
                        <el-option
                          v-for="callType in getSoftKeyCallTypeList(scope.row)"
                          :key="callType.value"
                          :label="callType.label"
                          :value="callType.value"
                        />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('writeFreq.sms')" min-width="85">
                  <template #default="scope">
                    <el-form-item v-if="!(scope.row.callId === 0xffff || scope.row.callType !== SoftKeyCallType.MSG)" label-width="0" class="!m-0">
                      <bfSelect
                        v-model="scope.row.smsId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        popper-class="sms-selection-container"
                      >
                        <el-option v-for="sms in smsContent" :key="sms.msgId" :label="sms.msgContent" :value="sms.msgId" />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <!-- <el-col :span="8">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span>{{ $t('writeFreq.longPressNumCallTable') }}</span>
              </el-divider>
              <el-table
                :data="buttonDefined.longPressNumCallTable"
                class="bf-table"
                :empty-text="$t('msgbox.emptyText')"
              >
                <el-table-column
                  label=""
                  type="index"
                />
                <el-table-column
                  :label="$t('dialog.callTarget')"
                  min-width="100"
                >
                  <template #default="scope">
                    <el-form-item label-width="0">
                      <bfSelect
                        v-model="scope.row.callId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="(val) => buttonDefinedCallIdChange(scope.row, val)"
                      >
                        <el-option
                          v-for="item in buttonDefineAddressList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('dialog.callType')"
                  min-width="85"
                >
                  <template #default="scope">
                    <el-form-item
                      v-if="scope.row.callId !== 0xFFFF"
                      label-width="0"
                    >
                      <bfSelect
                        v-model="scope.row.callType"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="(val) => buttonDefinedCallTypeChange(scope.row, val)"
                      >
                        <el-option
                          v-for="callType in getSoftKeyCallTypeList(scope.row)"
                          :key="callType.value"
                          :label="callType.label"
                          :value="callType.value"
                        />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('writeFreq.sms')"
                  min-width="85"
                >
                  <template #default="scope">
                    <el-form-item
                      v-if="!(scope.row.callId === 0xFFFF || scope.row.callType !==
                        SoftKeyCallType.MSG)"
                      label-width="0"
                    >
                      <bfSelect
                        v-model="scope.row.smsId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        popper-class="sms-selection-container"
                      >
                        <el-option
                          v-for="sms in smsContent"
                          :key="sms.msgId"
                          :label="sms.msgContent"
                          :value="sms.msgId"
                        />
                      </bfSelect>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col> -->
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--短信内容-->
      <el-tab-pane lazy :label="$t('dialog.smsContent')" name="smsContent" class="h-full settings-box">
        <shortMessage :ref="refSms" v-model="smsContent" />
      </el-tab-pane>
      <!--加密配置   -->
      <el-tab-pane lazy :label="$t('writeFreq.encryptionConfig')" name="encryptSettings" class="h-full">
        <encryptSettings
          v-model:encryptEnable="encryptConfig.config.encryptEnable"
          v-model:encryptXORList="encryptList"
          v-model:encryptARC4List="encryptARC4List"
          v-model:encryptAES256List="encryptAES256List"
          :encryptListLimit="encryptListLimit"
        />
      </el-tab-pane>
      <!--菜单设置-->
      <el-tab-pane lazy :label="$t('dialog.menuSettings')" name="menuSettings" class="menu-settings-box settings-box">
        <el-form ref="menuSettings" class="menu-settings-form" :model="menuSettings" label-position="top">
          <el-row :gutter="20" align="middle" class="no-margin-x has-input-row !w-[calc(100%_-_20px)]">
            <el-col :span="8">
              <el-form-item :label="$t('dialog.menuHangTime') + ':'" prop="hangTime">
                <bfInputNumberV2 v-model="menuSettings.hangTime" class="!w-full" step-strictly :min="0" :max="30" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.idShownMinLen') + ':'" prop="baseConfig.idShownMinLen">
                <bfInputNumberV2 v-model="idShownMinLen" class="!w-full" step-strictly :min="1" :max="8" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.lcdHangTime') + ':'" prop="hangTime">
                <bfInputNumberV2 v-model="menuSettings.lcdHangTime" class="!w-full" step-strictly :min="0" :max="30" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.keyboardHangTime') + ':'" prop="hangTime">
                <bfInputNumberV2 v-model="menuSettings.keyboardHangTime" class="!w-full" step-strictly :min="0" :max="30" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.chDisplayMode') + ':'" prop="baseSetting.chDisplayMode">
                <bfSelect
                  v-model="menuSettings.baseSetting.chDisplayMode"
                  class="!w-full !h-[50px]"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in freqDisplayList" :key="i" :label="item.label" :value="item.value" />
                </bfSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.callDisplayMode') + ':'">
                <bfSelect
                  v-model="menuSettings.baseSetting.callDisplayMode"
                  class="!w-full !h-[50px]"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in callDisplayModeList" :key="i" :label="item.label" :value="item.value" />
                </bfSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.manualQuickCallType') + ':'">
                <bfSelect
                  v-model="menuSettings.baseConfig.manualQuickCallType"
                  class="!w-full !h-[50px]"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in manualQuickCallList" :key="i" :label="item.label" :value="item.value" />
                </bfSelect>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item prop="baseSetting.numberKeyFastDial">
                <bfCheckbox v-model="menuSettings.baseSetting.numberKeyFastDial">
                  <span v-text="$t('dialog.quickDailEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.baseSetting.menuOff">
                  <span v-text="$t('dialog.closeMenuButton')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.baseSetting.callDirectionEnable">
                  {{ $t('writeFreq.callDirectionEnable') }}
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.baseConfig.AliasDisp">
                  {{ $t('writeFreq.aliasDisp') }}
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.baseConfig.bBootDisp">
                  {{ $t('writeFreq.bootInterfaceDisp') }}
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.zoneConfig.enable">
                  {{ $t('dialog.area') }}
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.bluetoothConfig.enable">
                  {{ $t('writeFreq.bluetooth') }}
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--通讯录-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.addressBook')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="contactConfig.contacts">
                <bfCheckbox v-model="menuSettings.contactConfig.contacts" @change="contactsChange">
                  <span v-text="$t('dialog.addressBook')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactConfig.contactList">
                <bfCheckbox v-model="menuSettings.contactConfig.contactList" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.contactList')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactConfig.contactGroup">
                <bfCheckbox v-model="menuSettings.contactConfig.contactGroup" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.contactGroup')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactConfig.manualDialing">
                <bfCheckbox v-model="menuSettings.contactConfig.manualDialing" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.manualDialing')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactConfig.newContact">
                <bfCheckbox v-model="menuSettings.contactConfig.newContact" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.newContact')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactConfig.groupManagement">
                <bfCheckbox v-model="menuSettings.contactConfig.groupManagement" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.groupManagement')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.editEnable">
                <bfCheckbox v-model="menuSettings.addressSetting.editEnable" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.editContact')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.deleteEnable">
                <bfCheckbox v-model="menuSettings.addressSetting.deleteEnable" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.contactDelete')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.groupJoinOrExit">
                <bfCheckbox v-model="menuSettings.addressSetting.groupJoinOrExit" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.groupJoinOrExit')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.sendSms">
                <bfCheckbox v-model="menuSettings.addressSetting.sendSms" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.sendMessages')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.callTip">
                <bfCheckbox v-model="menuSettings.addressSetting.callTip" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.callReminder')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.deviceDetect">
                <bfCheckbox v-model="menuSettings.addressSetting.deviceDetect" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.interphoneDetection')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.remoteMonitor">
                <bfCheckbox v-model="menuSettings.addressSetting.remoteMonitor" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.remoteMonitor')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="addressSetting.deviceActive">
                <bfCheckbox v-model="menuSettings.addressSetting.deviceActive" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.deviceActive')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceControl.deviceRemoteDeath">
                <bfCheckbox v-model="menuSettings.deviceControl.deviceRemoteDeath" :disabled="isContactMenuEnable">
                  <span v-text="$t('dialog.deviceRemoteDeath')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactGroup.newSingleContact">
                <bfCheckbox v-model="menuSettings.contactGroup.newSingleContact" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.newSingleContact')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactGroup.newGroupContact">
                <bfCheckbox v-model="menuSettings.contactGroup.newGroupContact" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.newGroupContact')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactGroup.newGroup">
                <bfCheckbox v-model="menuSettings.contactGroup.newGroup" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.newGroup')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="contactGroup.editGroup">
                <bfCheckbox v-model="menuSettings.contactGroup.editGroup" :disabled="isContactMenuEnable">
                  <span v-text="$t('writeFreq.editGroup')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--电话本-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.phoneBook')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="phoneConfig.enable">
                <bfCheckbox v-model="menuSettings.phoneConfig.enable" @change="phoneConfigEnableChange">
                  <span v-text="$t('writeFreq.phoneBook')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="phoneConfig.phoneList">
                <bfCheckbox v-model="menuSettings.phoneConfig.phoneList" :disabled="isPhoneConfigMenuEnable">
                  <span v-text="$t('dialog.contactList')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="phoneConfig.manualDial">
                <bfCheckbox v-model="menuSettings.phoneConfig.manualDial" :disabled="isPhoneConfigMenuEnable">
                  <span v-text="$t('dialog.manualDialing')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="phoneConfig.newContact">
                <bfCheckbox v-model="menuSettings.phoneConfig.newContact" :disabled="isPhoneConfigMenuEnable">
                  <span v-text="$t('dialog.newContact')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="phoneConfig.editContact">
                <bfCheckbox v-model="menuSettings.phoneConfig.editContact" :disabled="isPhoneConfigMenuEnable">
                  <span v-text="$t('writeFreq.editContact')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="phoneConfig.deleteContact">
                <bfCheckbox v-model="menuSettings.phoneConfig.deleteContact" :disabled="isPhoneConfigMenuEnable">
                  <span v-text="$t('writeFreq.contactDelete')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--扫描-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.scan')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="scanConfig.scanEnable">
                <bfCheckbox v-model="menuSettings.scanConfig.menuEnable" @change="scanMenuEnableChange">
                  <span v-text="$t('writeFreq.scan')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="scanConfig.scanEnable">
                <bfCheckbox v-model="menuSettings.scanConfig.scanEnable" :disabled="menuScanDisable">
                  <span v-text="$t('dialog.scanEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="scanConfig.editScanList">
                <bfCheckbox v-model="menuSettings.scanConfig.editScanList" :disabled="menuScanDisable">
                  <span v-text="$t('dialog.editScanList')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--漫游-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.roaming')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="scanConfig.scanEnable">
                <bfCheckbox v-model="menuSettings.roamConfig.menuEnable" @change="roamMenuEnableChange">
                  <span v-text="$t('writeFreq.roaming')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="scanConfig.scanEnable">
                <bfCheckbox v-model="menuSettings.roamConfig.roamEnable" :disabled="menuRoamDisable">
                  <span v-text="$t('writeFreq.roamEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="scanConfig.editScanList">
                <bfCheckbox v-model="menuSettings.roamConfig.editRoamList" :disabled="menuRoamDisable">
                  <span v-text="$t('dialog.editScanList')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.roamConfig.lockSite" :disabled="menuRoamDisable">
                  <span v-text="$t('writeFreq.siteLock')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="menuSettings.roamConfig.manualRoam" :disabled="menuRoamDisable">
                  <span v-text="$t('writeFreq.manualSiteRoam')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--别名-->
          <!--          <el-row-->
          <!--              :gutter='20'-->
          <!--              class='no-margin-x'-->
          <!--              type='flex'-->
          <!--              align='middle'-->
          <!--          >-->
          <!--            <el-divider>-->
          <!--              <el-icon><CaretBottom /></el-icon>-->
          <!--              <span v-text='$t("writeFreq.alias")'></span>-->
          <!--            </el-divider>-->
          <!--            <el-col-->
          <!--                :xs="24"-->
          <!--                :sm="12"-->
          <!--            >-->
          <!--              <el-form-item prop="timeSlotSetting.aliasMenuEnable">-->
          <!--                <el-checkbox v-model="menuSettings.deviceConfig3.aliasEnable">-->
          <!--                  <span v-text="$t('writeFreq.alias')"></span>-->
          <!--                </el-checkbox>-->
          <!--              </el-form-item>-->
          <!--            </el-col>-->
          <!--          </el-row>-->
          <!--短信-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.sms')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="smsConfig.enable">
                <bfCheckbox v-model="menuSettings.smsConfig.enable" @change="smsMenuEnableChange">
                  <span v-text="$t('writeFreq.sms')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsConfig.newSms">
                <bfCheckbox v-model="menuSettings.smsConfig.newSms" :disabled="isSmsMenuEnable">
                  <span v-text="$t('dialog.newSmsMessage')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsConfig.presetSms">
                <bfCheckbox v-model="menuSettings.smsConfig.presetSms" :disabled="isSmsMenuEnable">
                  <span v-text="$t('dialog.preMadeSms')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsConfig.receiveBox">
                <bfCheckbox v-model="menuSettings.smsConfig.receiveBox" :disabled="isSmsMenuEnable">
                  <span v-text="$t('dialog.inbox')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsConfig.sendBox">
                <bfCheckbox v-model="menuSettings.smsConfig.sendBox" :disabled="isSmsMenuEnable">
                  <span v-text="$t('dialog.outbox')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsConfig.clearSms">
                <bfCheckbox v-model="menuSettings.smsConfig.clearSms" :disabled="isSmsMenuEnable">
                  <span v-text="$t('writeFreq.clearSms')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsOperation.reply">
                <bfCheckbox v-model="menuSettings.smsOperation.reply" :disabled="isSmsMenuEnable">
                  <span v-text="$t('writeFreq.reply')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsOperation.forward">
                <bfCheckbox v-model="menuSettings.smsOperation.forward" :disabled="isSmsMenuEnable">
                  <span v-text="$t('writeFreq.forward')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsOperation.resend">
                <bfCheckbox v-model="menuSettings.smsOperation.resend" :disabled="isSmsMenuEnable">
                  <span v-text="$t('writeFreq.resend')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="smsOperation.delete">
                <bfCheckbox v-model="menuSettings.smsOperation.delete" :disabled="isSmsMenuEnable">
                  <span v-text="$t('dialog.delete')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--呼叫记录-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.callRecord')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="callConfig.missedRecord">
                <bfCheckbox v-model="menuSettings.callConfig.unReceivedCall">
                  <span v-text="$t('dialog.missedCall')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="callConfig.receivedRecord">
                <bfCheckbox v-model="menuSettings.callConfig.receivedCall">
                  <span v-text="$t('dialog.answeredCall')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="callConfig.outgoingRecord">
                <bfCheckbox v-model="menuSettings.callConfig.callOut">
                  <span v-text="$t('dialog.outgoingCall')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--录音-->
          <el-row v-if="isLConfig" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.recording')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="recordConfig.menuEnable">
                <bfCheckbox v-model="menuSettings.recordConfig.menuEnable" @change="recordingChange">
                  <span v-text="$t('dialog.recordEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="recordConfig.recordEnable">
                <bfCheckbox v-model="menuSettings.recordConfig.recordEnable" :disabled="isRecordMenuEnable">
                  <span v-text="$t('dialog.recordEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="recordConfig.recordFile">
                <bfCheckbox v-model="menuSettings.recordConfig.recordFile" :disabled="isRecordMenuEnable">
                  <span v-text="$t('writeFreq.recordFile')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--配置-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.configure')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.menuEnable">
                <bfCheckbox v-model="menuSettings.deviceConfig.menuEnable">
                  <span v-text="$t('dialog.configure')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceInfo.infoMenuEnable">
                <bfCheckbox v-model="menuSettings.deviceInfo.infoMenuEnable">
                  <span v-text="$t('writeFreq.interphoneInfo')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--对讲机配置-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.interphoneConfig')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.deviceSetting">
                <bfCheckbox v-model="menuSettings.deviceConfig.deviceSetting" @change="deviceConfigChange">
                  <span v-text="$t('writeFreq.interphoneConfig')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig2.locale">
                <bfCheckbox v-model="menuSettings.deviceConfig2.locale" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('writeFreq.langEnv')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.offline">
                <bfCheckbox v-model="menuSettings.deviceConfig.offline" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.offNetwork')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.toneTip">
                <bfCheckbox v-model="menuSettings.deviceConfig.toneTip" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.toneOrTip')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.transmitPower">
                <bfCheckbox v-model="menuSettings.deviceConfig.transmitPower" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.txPower')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.backLight">
                <bfCheckbox v-model="menuSettings.deviceConfig.backLight" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.backlight')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.bootInterface">
                <bfCheckbox v-model="menuSettings.deviceConfig.bootInterface" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.bootInterface')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig2.powerOnPassword">
                <bfCheckbox v-model="menuSettings.deviceConfig2.powerOnPassword" :disabled="menuSettings.powerOnPwd.length !== 6 && isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.powerOnPwd')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig.keyboardLock">
                <bfCheckbox v-model="menuSettings.deviceConfig.keyboardLock" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.keyboardLock')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig2.ledIndicator">
                <bfCheckbox v-model="menuSettings.deviceConfig2.ledIndicator" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.ledIndicator')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig2.quieting">
                <bfCheckbox v-model="menuSettings.deviceConfig2.quieting" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.Quieting')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig2.soundCtrl">
                <bfCheckbox v-model="menuSettings.deviceConfig2.soundCtrl" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.voiceControl')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col v-if="isGConfig" :span="8">
              <el-form-item prop="deviceConfig2.locate">
                <bfCheckbox v-model="menuSettings.deviceConfig2.locate" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.satellitePosition')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col v-if="isGConfig || isLConfig" :span="8">
              <el-form-item prop="deviceConfig2.timeSetting">
                <bfCheckbox v-model="menuSettings.deviceConfig2.timeSetting" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('dialog.timeSetting')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <!--            <el-col-->
            <!--                :xs="24"-->
            <!--                :sm="12" v-if="deviceWriteInfo.config.roam"-->
            <!--            >-->
            <!--              <el-form-item prop="roamConfig.menuEnable">-->
            <!--                <el-checkbox v-model="menuSettings.roamConfig.menuEnable">-->
            <!--                  <span v-text="$t('dialog.roamInterface')"></span>-->
            <!--                </el-checkbox>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :span="8">
              <el-form-item prop="deviceConfig3.channelLockEnable">
                <bfCheckbox v-model="menuSettings.deviceConfig3.channelLockEnable" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('writeFreq.softKeyFuncDefine.CH_LOCK_SW')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig3.denoiseEnable">
                <bfCheckbox v-model="menuSettings.deviceConfig3.denoiseEnable" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('writeFreq.denoise')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deviceConfig3.aliasEnable">
                <bfCheckbox v-model="menuSettings.deviceConfig3.aliasEnable" :disabled="isDeviceConfigMenuEnable">
                  <span v-text="$t('writeFreq.aliasMenu')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
          <!--信道配置-->
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.channelConfig')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item prop="channelSetting.chConfigEnable">
                <bfCheckbox v-model="menuSettings.channelSetting.channelConfigMenuEnable" @change="channelConfigMenuEnableChange">
                  <span v-text="$t('dialog.chConfigSwitch')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channelSetting.receivingFrequency">
                <bfCheckbox v-model="menuSettings.channelSetting.receivingFreq" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.receiveFrequency')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channelSetting.transmittingFrequency">
                <bfCheckbox v-model="menuSettings.channelSetting.transmittingFreq" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.transFrequency')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channelSetting.channelName">
                <bfCheckbox v-model="menuSettings.channelSetting.channelName" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.chName')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channelSetting.roamInterface">
                <bfCheckbox v-model="menuSettings.channelSetting.transmitTimeLimit" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.transTimeLimit')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channelSetting.subAudioSetting">
                <bfCheckbox v-model="menuSettings.channelSetting.subAudioSetting" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.subaudioSetting')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channelSetting.launchContact">
                <bfCheckbox v-model="menuSettings.channelSetting.launchContact" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.launchContact')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channelSetting.colorCode">
                <bfCheckbox v-model="menuSettings.channelSetting.colorCode" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.colorCodes')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="timeSlotSetting.timeSlot">
                <bfCheckbox v-model="menuSettings.timeSlotSetting.timeSlot" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.timeSlots')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <!--            <el-col :xs="24" :sm="12" v-if='false'>-->
            <!--              <el-form-item prop="timeSlotSetting.virtualClusterTimeSlot">-->
            <!--                <el-checkbox v-model="menuSettings.timeSlotSetting.virtualClusterTimeSlot">-->
            <!--                  <span v-text="$t('dialog.virtualTimeSlot')"></span>-->
            <!--                </el-checkbox>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :span="8">
              <el-form-item prop="timeSlotSetting.receivingList">
                <bfCheckbox v-model="menuSettings.timeSlotSetting.receivingList" :disabled="channelConfigMenuEnable">
                  <span v-text="$t('dialog.receivingList')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--卫星定位-->
      <el-tab-pane v-if="isGConfig" lazy :label="$t('dialog.satellitePosition')" name="gpsData">
        <el-form ref="gpsData" class="gpsData-form" :model="gpsData" label-position="top">
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]">
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.gpsMode') + ':'">
                <bfSelect v-model="gpsMode" class="!w-full !h-[50px]" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                  <el-option v-for="(item, i) in gpsModeList" :key="i" :label="item.label" :value="item.value" />
                </bfSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.connectionTimes') + ':'">
                <bfInputNumberV2
                  v-model="gpsData.connectionCount"
                  class="!w-full"
                  :disabled="!gpsData.baseConfig.enable"
                  step-strictly
                  :min="0"
                  :max="255"
                  :step="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.pttTimes') + ':'">
                <bfInputNumberV2
                  v-model="gpsData.pttCount"
                  class="!w-full"
                  :disabled="!gpsData.baseConfig.enable"
                  step-strictly
                  :min="0"
                  :max="255"
                  :step="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.controlCenter') + ':'">
                <bfInputNumberV2
                  v-model="gpsData.centerId"
                  class="!w-full"
                  :disabled="!gpsData.baseConfig.enable"
                  step-strictly
                  :min="1"
                  :max="16777215"
                  :step="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.queryCommand') + ':'">
                <bfInput
                  v-model="gpsData.queryCmd"
                  :disabled="!gpsData.baseConfig.enable"
                  @input="val => (gpsData.queryCmd = gpsSettingsQueryCmdInputEvent(val))"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--录音-->
      <el-tab-pane v-if="isLConfig" lazy :label="$t('dialog.recording')" name="recording" class="recording-box settings-box has-tabs-child">
        <record-list-v2
          ref="recordListRef"
          :record-list="recordList"
          :is-reading="isReading"
          :is-writing="isWriting"
          :dis-read-btn="disReadBtn"
          @get-record-list="getRecordList"
          @download-record="beforeDownload"
        />
      </el-tab-pane>
      <!--信令系统-->
      <el-tab-pane lazy :label="$t('writeFreq.signalingSystem')" name="signalingSystem" class="h-full settings-box">
        <el-form class="signaling-config-form" :model="signalingSystem" label-position="top">
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="signalingSystem.remoteConfig.remoteShutDecodeEnable">
                  <span v-text="$t('writeFreq.deviceRemoteDeadDecode')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="signalingSystem.remoteConfig.remoteMonitorDecode">
                  <span v-text="$t('writeFreq.remoteMonitorDecode')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="signalingSystem.remoteConfig.remoteNoticeDecode">
                  <span v-text="$t('writeFreq.remotePromptDecode')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="signalingSystem.remoteConfig.remoteDetectDecode">
                  <span v-text="$t('writeFreq.remoteDetectionDecode')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.remoteMonitorDuration') + ':'">
                <bfInputNumberV2 v-model="signalingSystem.remoteMonitorDuration" class="!w-full" step-strictly :min="10" :max="120" :step="10" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.callPromptTimes') + ':'">
                <bfInputNumberV2 v-model="signalingSystem.remoteAlertCount" class="!w-full" step-strictly :min="0" :max="255" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.authentication')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="signalingSystem.signalingEncrypt.remoteShutEncryptEnable">
                  <span v-text="$t('writeFreq.remoteShutEncryptEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <bfCheckbox v-model="signalingSystem.signalingEncrypt.remoteMonitorDecodeEnable">
                  <span v-text="$t('writeFreq.remoteMonitorDecodeEnable')" />
                </bfCheckbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.signalingPwd') + ':'" prop="signalingPwd">
                <bfInput v-model="signalingSystem.signalingPwd" type="password" :maxlength="16" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--警报-->
      <el-tab-pane lazy :label="$t('writeFreq.theAlarm')" name="alertSetting" class="h-full settings-box has-tabs-child">
        <el-tabs class="bf-tab h-full" type="border-card" model-value="alertConfig">
          <el-tab-pane :label="$t('writeFreq.alarmSetting')" name="alertConfig" class="h-full">
            <el-form class="alert-config-form" :model="alertConfig" label-position="top">
              <el-row v-if="deviceWriteInfo.config.workAlone" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
                <el-divider>
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span v-text="$t('writeFreq.workAlone')" />
                </el-divider>
                <el-col :span="8">
                  <el-form-item>
                    <bfCheckbox v-model="alertConfig.aloneWorkEnable">
                      <span v-text="$t('writeFreq.workAlone')" />
                    </bfCheckbox>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.workResTimeAlone') + ':'">
                    <bfInputNumberV2
                      v-model="alertConfig.responseTime"
                      class="!w-full"
                      :disabled="workAloneUnEnable"
                      step-strictly
                      :min="1"
                      :max="255"
                      :step="1"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.workAloneReminderTime') + ':'">
                    <bfInputNumberV2
                      v-model="alertConfig.remindTime"
                      class="!w-full"
                      :disabled="workAloneUnEnable"
                      step-strictly
                      :min="0"
                      :max="255"
                      :step="1"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.workResOptAlone') + ':'">
                    <bfSelect
                      v-model="alertConfig.responseOperation"
                      class="!w-full !h-[50px]"
                      :placeholder="$t('dialog.select')"
                      :disabled="workAloneUnEnable"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in aloneWorkOptList" :key="i" :label="item.label" :value="item.value" />
                    </bfSelect>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="deviceWriteInfo.config.upendAlarm" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
                <el-divider>
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span v-text="$t('writeFreq.runBackward')" />
                </el-divider>
                <el-col :span="8">
                  <el-form-item>
                    <bfCheckbox v-model="alertConfig.upendEnable">
                      <span v-text="$t('writeFreq.upsideDownSwitch')" />
                    </bfCheckbox>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.triggerMode') + ':'">
                    <bfSelect
                      v-model="alertConfig.upendConfig.triggerMode"
                      class="!w-full !h-[50px]"
                      :placeholder="$t('dialog.select')"
                      :disabled="upsideDownUnEnable"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="item in triggerModeList" :key="item.label" :label="item.label" :value="item.value" />
                    </bfSelect>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.triggerInclination') + ':'">
                    <bfSelect
                      v-model="alertConfig.upendConfig.triggerTilt"
                      class="!w-full !h-[50px]"
                      :placeholder="$t('dialog.select')"
                      :disabled="upsideDownUnEnable"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="item in triggerTiltList" :key="item.label" :label="item.label" :value="item.value" />
                    </bfSelect>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.entryDelay') + ':'">
                    <bfInputNumberV2
                      v-model="alertConfig.entryDelay"
                      class="!w-full"
                      :disabled="upsideDownUnEnable"
                      step-strictly
                      :min="5"
                      :max="255"
                      :step="1"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.exitDelay') + ':'">
                    <bf-input-number-v2
                      v-model="alertConfig.quitDelay"
                      :disabled="upsideDownUnEnable"
                      :formatter="val => (val === 255 ? $t('writeFreq.unlimited') : val)"
                      class="!w-full"
                      step-strictly
                      :min="0"
                      :max="255"
                      :step="1"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('writeFreq.promptTimeBackwards') + ':'">
                    <bfInputNumberV2
                      v-model="alertConfig.upendConfig.preRewindTime"
                      class="!w-full"
                      :disabled="upsideDownUnEnable"
                      step-strictly
                      :min="0"
                      :max="10"
                      :step="1"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('writeFreq.digitalEmergencyAlarm')" name="digitAlarmList" class="h-full flex">
            <div class="basis-1/6 flex justify-center">
              <div class="w-[122px] flex flex-col gap-y-2">
                <div
                  v-for="(item, index) in digitAlarmList"
                  :key="index"
                  class="flex justify-center items-center shadow-[inset_0_0_0_2px_rgba(148,204,232,1)] h-[42px] w-[122px]"
                  :class="{
                    selected: digitAlarmIndex === index,
                    disToggle: !digitAlarm.name,
                  }"
                  @click="digitAlarmLabelClick(item, index)"
                >
                  <span class="list-item-name text-[#fff] mr-2" v-text="item.name" />
                  <el-button type="danger" icon="delete" size="small" circle @click.stop="deleteDigitAlarm(item, index)" />
                </div>
                <bf-button color-type="primary" icon="circle-plus" :disabled="disAddDigital" @click="addOneDigitAlarm" />
              </div>
            </div>
            <div v-if="digitAlarmList.length" class="basis-5/6">
              <el-form ref="digitAlarm" class="digit-alarm-config-form" :model="digitAlarm" :rules="digitAlarmRules" label-position="top">
                <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]">
                  <el-col :span="12">
                    <el-form-item :label="$t('dialog.name') + ':'" prop="name">
                      <bf-input v-model="digitAlarm.name" :maxlength="16" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('dataTable.alarmType') + ':'">
                      <bf-select
                        v-model="digitAlarm.type"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        class="!w-full !h-[50px]"
                      >
                        <el-option v-for="(item, i) in digitAlarmTypeList" :key="i" :label="item.label" :value="item.value" />
                      </bf-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('dialog.mode') + ':'">
                      <bf-select
                        v-model="digitAlarm.mode"
                        :disabled="forbidAlarm"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        class="!w-full !h-[50px]"
                      >
                        <el-option v-for="(item, i) in digitAlarmModeList" :key="i" :label="item.label" :value="item.value" />
                      </bf-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('writeFreq.replyChannel') + ':'">
                      <bf-select
                        v-model="digitAlarm.replyChannel"
                        :disabled="forbidReplyChannel"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        class="!w-full !h-[50px]"
                      >
                        <el-option v-for="(item, i) in replyChannelList" :key="i" :label="item.label" :value="item.value" />
                      </bf-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('writeFreq.notPoliteRetry') + ':'">
                      <bf-input-number-v2
                        v-model="digitAlarm.impoliteRetry"
                        :disabled="forbidAlarm"
                        step-strictly
                        :min="1"
                        :max="15"
                        :step="1"
                        class="!w-full h-[50px]"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('writeFreq.politeRetry') + ':'">
                      <bf-input-number-v2
                        v-model="digitAlarm.politeRetry"
                        :disabled="!disMicActiveTime || forbidAlarm"
                        step-strictly
                        :min="0"
                        :max="14"
                        :step="1"
                        class="!w-full h-[50px]"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('writeFreq.micActiveTime') + ':'">
                      <bf-input-number-v2
                        v-model="digitAlarm.hotMicDuration"
                        :disabled="disMicActiveTime"
                        step-strictly
                        :min="10"
                        :max="120"
                        :step="10"
                        class="!w-full h-[50px]"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item>
                      <bf-checkbox v-model="digitAlarm.config.autoSendGps">
                        <span v-text="$t('writeFreq.alarmAutoSendGps')" />
                      </bf-checkbox>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--通讯录-->
      <el-tab-pane :label="$t('dialog.addressBook')" name="addressBook" class="h-full settings-box address-book">
        <addressBook
          :ref="addrBookTreeId"
          class="settings-box"
          :treeId="addrBookTreeId"
          :redrawTree="writerFrequencyTabName === 'addressBook'"
          :callTypes="addressBookCallTypes"
          :addressBook="originAddressBook"
          :max-size="1024"
          @select="selectAddressBooks"
        />
      </el-tab-pane>
      <!--通讯录群组-->
      <el-tab-pane :label="$t('writeFreq.addressBookGroup')" name="addressBookGroup" class="h-full settings-box has-tabs-child flex">
        <div class="basis-1/6 flex justify-center">
          <div class="w-[142px] flex flex-col gap-y-2">
            <div
              v-for="(item, index) in addressBookGroup"
              :key="index"
              class="flex justify-center items-center shadow-[inset_0_0_0_2px_rgba(148,204,232,1)] h-[42px] w-[142px]"
              :class="{
                selected: addressBookGroupIndex === index,
                disToggle: !addressBookGroupItem.name,
              }"
              @click="addressBookGroupLabelClick(item, index)"
            >
              <span class="text-white mr-2" v-text="item.name" />
              <el-button type="danger" icon="delete" size="small" circle @click.stop="deleteAddressBookGroupItem(item, index)" />
            </div>
            <bfButton color-type="primary" icon="circle-plus" :disabled="disAddBookGroup" @click="addOneAddressBookGroup" />
          </div>
        </div>
        <div v-if="addressBookGroup.length" class="basis-5/6">
          <addressBookGroup
            ref="addressBookGroup"
            v-model="addressBookGroup"
            v-model:addressBook="selectedAddressBook"
            v-model:dataId="currAddressBookGroupId"
            :limit="addressBookGroupLimit"
          />
        </div>
      </el-tab-pane>
      <!--接收组列表-->
      <el-tab-pane :label="$t('dialog.rxGroup')" name="rxGroup" class="h-full settings-box">
        <receiveGroup
          :ref="refReceiveGroup"
          v-model="rxGroupList"
          :channels="selectedChannels"
          :addressTreeId="addrBookTreeId"
          :max-size="32"
          :getDefaultAddress="getDefaultAddress"
          :getAddressName="getAddressNameByDmrId"
          :getOriginAddress="getOriginAddressBook"
        />
      </el-tab-pane>
      <!--信道设置-->
      <el-tab-pane :label="$t('dialog.channelSetting')" name="channelSettings" class="h-full settings-box has-tabs-child">
        <el-tabs v-model="channelDataTabs" class="bf-tab channel-list-tabs settings-tabs-box h-full" type="border-card">
          <el-tab-pane :label="$t('writeFreq.areaList')" name="areaList" class="h-full">
            <el-table
              id="area-channel-table"
              ref="zoneTable"
              :data="zoneDataList"
              :default-sort="{ prop: 'areaId', order: 'ascending' }"
              border
              stripe
              highlight-current-row
              :row-class-name="zoneTableRowClassName"
              height="calc(100% - 1px)"
              style="width: 100%"
              class="bf-table"
            >
              <el-table-column type="expand" label="#">
                <template #default="props">
                  <el-table
                    ref="channelTable"
                    :data="getZoneChannels(props.row.chIdList)"
                    :default-sort="{ prop: 'chId', order: 'ascending' }"
                    :row-class-name="channelTableRowClassName"
                    border
                    stripe
                    highlight-current-row
                    style="width: 100%"
                    class="bf-table"
                    @row-click="zoneChannelListClick"
                    @row-dblclick="zoneChannelListDblclick"
                  >
                    <el-table-column :label="$t('dialog.chId')" prop="chId" sortable />
                    <el-table-column :label="$t('dialog.chName')" prop="chName" sortable />
                    <el-table-column :label="$t('dialog.rxFrequency')" sortable>
                      <template #default="scope">
                        <span v-text="frequencyHz2Mhz(scope.row.rxFreq)" />
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('dialog.txFrequency')" sortable>
                      <template #default="scope">
                        <span v-text="frequencyHz2Mhz(scope.row.txFreq)" />
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column :label="$t('writeFreq.areaId')" prop="areaId" sortable />
              <el-table-column :label="$t('dialog.areaName')" sortable>
                <template #default="scope">
                  <bfInput v-if="scope.row.editing" v-model="scope.row.areaName" />
                  <span v-else v-text="scope.row.areaName" />
                </template>
              </el-table-column>
              <el-table-column :label="$t('writeFreq.channelNumber')" sortable>
                <template #default="scope">
                  <span v-text="scope.row.chIdList.length" />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <template v-if="channelDataList.length > 0">
            <el-tab-pane :label="$t('dialog.channelSetting')" name="chSettings" class="h-full">
              <el-form ref="channelData" class="channel-setting-form" :model="oneChannel" :rules="channelRules" label-position="top">
                <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
                  <el-col :span="8">
                    <el-form-item :label="$t('dialog.chName') + ':'" prop="chName">
                      <bfInput v-model="oneChannel.chName" :maxlength="16" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('dialog.chType') + ':'">
                      <bfSelect v-model="oneChannel.chType" class="!w-full !h-[50px]" @change="chTypeChanged">
                        <el-option v-for="(item, i) in chTypeList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isAChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.broadBand') + ':'">
                      <bfSelect v-model="oneChannel.analogChannelConfig.bandwidthFlag" class="!w-full !h-[50px]">
                        <el-option v-for="(item, i) in bandwidthFlagList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="(isDChannel ? $t('writeFreq.scanOrRoamList') : $t('writeFreq.scanList')) + ':'">
                      <bfSelect v-model="oneChannel.scanListWrap" class="!w-full !h-[50px]" :disabled="disableScanList" @change="scanListWrapChange">
                        <el-option v-for="item in chScanRoamGroupList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.scanConfig.autoScan" :disabled="disAutoScan">
                        <span v-text="$t('writeFreq.autoScanning')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel && isRConfig" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.scanConfig.autoRoam" :disabled="disAutoRoam">
                        <span v-text="$t('writeFreq.autoRoaming')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isDChannel" :span="8">
                    <el-form-item :label="$t('dialog.squelchLevel') + ':'">
                      <bfInputNumberV2 v-model="oneChannel.powerConfig.squelchLevel" class="!w-full" step-strictly :min="0" :max="9" :step="1" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('dialog.colorCodes') + ':'">
                      <bfInputNumberV2 v-model="oneChannel.colorCode" class="!w-full" step-strictly :min="0" :max="15" :step="1" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel && isRConfig" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.scanConfig.ipSiteConnect" :disabled="sameFreq || networkingMode === 2">
                        <span v-text="$t('writeFreq.ipSiteConnection')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.scanConfig.allowOfflineSign" :disabled="sameFreq || networkingMode === 2">
                        <span v-text="$t('dialog.allowOffNetwork')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.scanConfig.onlyReceive" :disabled="networkingMode === 2">
                        <span v-text="$t('dialog.receiveOnly')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.alertConfig.smsUdpCompress">
                        <span v-text="$t('writeFreq.smsUdpCompress')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.voiceConfig.voiceCallEmbedding">
                        <span v-text="$t('writeFreq.voiceCallEmbedding')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.aliasConfig.enable">
                        <span v-text="$t('writeFreq.voiceCallEmbedAlias')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel && deviceWriteInfo.config.svt" :span="8">
                    <el-form-item :label="$t('writeFreq.networkingMode') + ':'">
                      <bfSelect v-model="networkingMode" class="!w-full !h-[50px]" @change="netWorkingModeChange">
                        <el-option v-for="item in networkingModeList" :key="item.label" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.SVTSiteInfo') + ':'">
                      <bfSelect v-model="oneChannel.virtualClusterSiteInfoListId" class="!w-full !h-[50px]" :disabled="!isSvtEnable">
                        <el-option v-for="item in SVTSiteInfoList" :key="item.label" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <!--            <el-col-->
                  <!--                :xs="24"-->
                  <!--                :sm="12"-->
                  <!--                v-if='isDChannel'-->
                  <!--            >-->
                  <!--              <el-form-item :label="$t('writeFreq.networkingMode')">-->
                  <!--                <el-select-->
                  <!--                    v-model="networkingMode"-->
                  <!--                    @change='netWorkingModeChange'-->
                  <!--                >-->
                  <!--                  <el-option-->
                  <!--                      v-for="item in networkingModeList"-->
                  <!--                      :key="item.label"-->
                  <!--                      :label="item.label"-->
                  <!--                      :value="item.value"-->
                  <!--                  >-->
                  <!--                  </el-option>-->
                  <!--                </el-select>-->
                  <!--              </el-form-item>-->
                  <!--            </el-col>-->
                  <!--            <el-col :xs="24" :sm="12" v-if='isDChannel && deviceWriteInfo.config.sdc'>-->
                  <!--              <el-form-item>-->
                  <!--                <el-checkbox v-model="oneChannel.networkConfig.networking">-->
                  <!--                  <span v-text="$t('writeFreq.networking')"></span>-->
                  <!--                </el-checkbox>-->
                  <!--              </el-form-item>-->
                  <!--            </el-col>-->
                  <el-col v-if="isDChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.alertConfig.localCall" :disabled="networkingMode === 0">
                        <span v-text="$t('writeFreq.localCall')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.timeSlotConfig.DCDMEnable" :disabled="!sameFreq">
                        <span v-text="$t('writeFreq.TDMAThroughMode')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.chTimeSlotCalibrator') + ':'">
                      <bfSelect v-model="oneChannel.timeSlotConfig.chSlotAdjust" class="!w-full !h-[50px]" :disabled="!throughEnable">
                        <el-option v-for="(item, i) in chTimeSlotCalList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('dialog.slotMode') + ':'">
                      <bfSelect
                        v-model="oneChannel.timeSlotConfig.timeSlot"
                        class="!w-full !h-[50px]"
                        :disabled="networkingMode === 2"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="(item, i) in slotModeList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('dialog.virtualTimeSlot') + ':'">
                      <bfSelect
                        v-model="oneChannel.timeSlotConfig.virtualTimeSlot"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :disabled="oneChannel.timeSlotConfig.timeSlot !== 2 || networkingMode === 2"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="(item, i) in virtualTimeSlotList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.voiceConfig.priorityInterrupt">
                        <span v-text="$t('writeFreq.priorityInterrupt')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.voicePriority') + ':'">
                      <bfInputNumberV2 v-model="oneChannel.voiceConfig.voicePriority" class="!w-full" step-strictly :min="0" :max="3" :step="1" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row v-if="!isAChannel" :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
                  <el-divider>
                    <el-icon class="divider-icon">
                      <CaretBottom />
                    </el-icon>
                    <span class="divider-label" v-text="$t('writeFreq.encryption')" />
                  </el-divider>
                  <el-col :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.encryptConfig.enable" :disabled="disEncryptConfigEnable">
                        <span v-text="$t('dialog.enable')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('writeFreq.encryptType') + ':'">
                      <bfSelect
                        v-model="oneChannel.encryptConfig.type"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :disabled="disEncryption"
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="encryptTypeChange"
                      >
                        <el-option v-for="(item, i) in encryptTypeList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('writeFreq.encryptionAlgorithm') + ':'">
                      <bfSelect
                        v-model="oneChannel.encryptConfig.algorithm"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :disabled="disEncryption"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="(item, i) in algorithmList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.encryptConfig.encryptKeyRandom" :disabled="disEncryption || oneChannel.encryptConfig.type === 0">
                        <span v-text="$t('writeFreq.randomKey')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.encryptConfig.decryptKeyRandom" :disabled="!enableAdvancedEncryption">
                        <span v-text="$t('writeFreq.multiKeyDecryption')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('writeFreq.keyList') + ':'">
                      <bfSelect
                        v-model="oneChannel.encryptListId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :disabled="disEncryption"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="item in encryptKeyList" :key="item.label" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
                  <el-divider>
                    <el-icon class="divider-icon">
                      <CaretBottom />
                    </el-icon>
                    <span class="divider-label" v-text="$t('dialog.receive')" />
                  </el-divider>
                  <el-col :span="8">
                    <el-form-item :label="$t('dialog.rxFrequency') + ':'" prop="rxFreq">
                      <frequencyMhz v-model="oneChannel.rxFreq" :maxlength="9" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <freqMapOffset
                      v-model="freqOffset"
                      v-model:dstFreq="oneChannel.txFreq"
                      :disabled="onlyReceive"
                      :srcFreq="oneChannel.rxFreq"
                      :freqRange="deviceWriteInfo.frequencyRange"
                    />
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('dialog.receiveGroupList') + ':'">
                      <bfSelect
                        v-model="oneChannel.groupList"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="item in receiveGroupList" :key="item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.alertConfig.emergencyAlertTip" :disabled="networkingMode !== 0">
                        <span v-text="$t('writeFreq.emergencyAlarmIndication')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.alertConfig.emergencyAlertConfirm" :disabled="!oneChannel.alertConfig.emergencyAlertTip">
                        <span v-text="$t('writeFreq.emergencyAlarmConfirm')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.alertConfig.emergencyCallTip" :disabled="networkingMode !== 0">
                        <span v-text="$t('writeFreq.emergencyCallAlert')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>

                  <el-col v-if="!isDChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.decoding') + ':'">
                      <bfSelect
                        v-model="oneChannel.subsonicDecode"
                        class="!w-full !h-[50px]"
                        filterable
                        :allow-create="isAChannel"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        @change="subsonicDecodeChange"
                      >
                        <el-option v-for="item in subtoneCodeDataList" :key="item.label" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isDChannel" :span="8">
                    <el-form-item :label="$t('dialog.tailSelection') + ':'">
                      <bfSelect
                        v-model="oneChannel.subtoneConfig.ctcssRxDps"
                        class="!w-full !h-[50px]"
                        :disabled="subsonicDecodeIsDigitalCode"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="item in tailToneList" :key="item.label" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
                  <el-divider>
                    <el-icon class="divider-icon">
                      <CaretBottom />
                    </el-icon>
                    <span class="divider-label" v-text="$t('dialog.emission')" />
                  </el-divider>
                  <el-col :span="8">
                    <el-form-item :label="$t('dialog.txFrequency') + ':'" prop="txFreq">
                      <frequencyMhz v-model="oneChannel.txFreq" :maxlength="9" :disabled="onlyReceive" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isDChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.encoding') + ':'">
                      <bfSelect
                        v-model="oneChannel.subsonicEncode"
                        class="!w-full !h-[50px]"
                        filterable
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        :disabled="onlyReceive"
                      >
                        <el-option v-for="item in subtoneCodeDataList" :key="item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isDChannel" :span="8">
                    <el-form-item :label="$t('dialog.plosive') + ':'">
                      <bfSelect
                        :key="Date.now()"
                        v-model="oneChannel.subtoneConfig.ctcssTxDps"
                        class="!w-full !h-[50px]"
                        :disabled="disableCtcssTxDps"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="item in tailToneList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('dialog.sendGroup') + ':'">
                      <bfSelect
                        v-model="oneChannel.defaultAddress"
                        class="!w-full !h-[50px]"
                        disabled
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="item in defaultAddressList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.emergencySystem') + ':'">
                      <bfSelect
                        v-model="oneChannel.emergencySysId"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        :disabled="networkingMode !== 0 || onlyReceive"
                      >
                        <el-option v-for="item in emergencySysIdList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('dialog.txPower') + ':'">
                      <bfSelect
                        v-model="oneChannel.powerConfig.powerType"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        :disabled="onlyReceive"
                      >
                        <el-option v-for="(item, i) in txPowerTypes" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('dialog.sendTimeLimiter') + ':'">
                      <bfInputNumberV2
                        v-model="oneChannel.transmissionLimit"
                        class="!w-full"
                        step-strictly
                        :min="15"
                        :max="495"
                        :step="15"
                        :disabled="onlyReceive"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('dialog.totPwdUpdateDelay') + ':'">
                      <bfInputNumberV2 v-model="oneChannel.TOTKeyDelay" class="!w-full" step-strictly :min="0" :max="255" :step="1" :disabled="onlyReceive" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item :label="$t('dialog.permissionConditions') + ':'">
                      <bfSelect
                        v-model="oneChannel.voiceConfig.permitConditions"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        :disabled="onlyReceive"
                      >
                        <el-option v-for="(item, i) in permissionConditionsList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="isDChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="confirmedDataSingleCall">
                        <span v-text="$t('writeFreq.confirmedDataSingleCall')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isAChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.voiceConfig.singleCallRes" :disabled="onlyReceive">
                        <span v-text="$t('dialog.singleCallConfirm')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isDChannel" :span="8">
                    <el-form-item :label="$t('writeFreq.busyChannelLock') + ':'">
                      <bfSelect
                        v-model="oneChannel.analogChannelConfig.flgBCL"
                        class="!w-full !h-[50px]"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                        :disabled="onlyReceive"
                      >
                        <el-option v-for="(item, i) in busyChannelLockList" :key="i" :label="item.label" :value="item.value" />
                      </bfSelect>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="!isDChannel" :span="8">
                    <el-form-item>
                      <bfCheckbox v-model="oneChannel.analogChannelConfig.tailNoise" :disabled="onlyReceive">
                        <span v-text="$t('dialog.tailCancellation')" />
                      </bfCheckbox>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-tab-pane>
          </template>
        </el-tabs>
      </el-tab-pane>
      <!--扫描-->
      <el-tab-pane :label="$t('writeFreq.scan')" name="scanFunction" class="h-full settings-box has-tabs-child flex">
        <div class="basis-1/6 flex justify-center">
          <div class="w-[122px] flex flex-col gap-y-2">
            <div
              v-for="(item, index) in scanList"
              :key="index"
              class="flex justify-center items-center shadow-[inset_0_0_0_2px_rgba(148,204,232,1)] h-[42px] w-[122px]"
              :class="{
                selected: scanGroupIndex === index,
                disToggle: !scanGroup.name,
              }"
              @click="scanGroupIndexLabelClick(item, index)"
            >
              <span class="list-item-name text-[#fff] mr-2" v-text="item.name" />
              <el-button type="danger" icon="delete" size="small" circle @click.stop="deleteScanGroup(item, index)" />
            </div>
            <bf-button color-type="primary" icon="circle-plus" :disabled="disAddScanGroup" @click="addOneScanGroup" />
          </div>
        </div>
        <div v-if="scanList.length" class="basis-5/6">
          <el-form ref="scanGroup" class="grid grid-cols-2 gap-x-3" :model="scanGroup" :rules="scanGroupRules" label-position="top">
            <el-form-item :class="['transfer-wrapper col-span-2 justify-self-center', locale]">
              <bfTransfer
                v-model="scanGroup.membersList"
                :titles="scanningGroupTransferTitles"
                :data="availableChannelList"
                :props="{
                  key: 'value',
                }"
                @change="scanListMembersChange"
              >
                <template #default="{ option }">
                  <span>{{ option.label }}</span>
                </template>
              </bfTransfer>
            </el-form-item>
            <el-form-item>
              <bfCheckbox v-model="scanGroup.config.reply">
                <span v-text="$t('dialog.answer')" />
              </bfCheckbox>
            </el-form-item>
            <el-form-item :label="$t('dialog.name') + ':'" prop="name">
              <bfInput v-model="scanGroup.name" :maxlength="16" />
            </el-form-item>
            <el-form-item :label="$t('writeFreq.firstPriorityChannel') + ':'">
              <bfSelect
                v-model="scanGroup.priority1Ch"
                class="!w-full !h-[50px]"
                :placeholder="$t('dialog.select')"
                filterable
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option v-for="(item, i) in priority1ChList" :key="i" :label="item.label" :value="item.value" />
              </bfSelect>
            </el-form-item>
            <el-form-item :label="$t('writeFreq.secondPriorityChannel') + ':'">
              <bfSelect
                v-model="scanGroup.priority2Ch"
                class="!w-full !h-[50px]"
                :placeholder="$t('dialog.select')"
                filterable
                :disabled="noPriority1Ch"
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option v-for="(item, i) in priority2ChList" :key="i" :label="item.label" :value="item.value" />
              </bfSelect>
            </el-form-item>
            <el-form-item :label="$t('writeFreq.specifyTransmitChannel') + ':'">
              <bfSelect
                v-model="scanGroup.appointTxCh"
                class="!w-full !h-[50px]"
                :placeholder="$t('dialog.select')"
                filterable
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option v-for="(item, i) in specifiedTrChIDList" :key="i" :label="item.label" :value="item.value" />
              </bfSelect>
            </el-form-item>
            <el-form-item :label="$t('writeFreq.scanningEmissionMode') + ':'">
              <bfSelect
                v-model="scanGroup.config.scanTxMode"
                class="!w-full !h-[50px]"
                :placeholder="$t('dialog.select')"
                filterable
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option v-for="(item, i) in scanTxModeList" :key="i" :label="item.label" :value="item.value" />
              </bfSelect>
            </el-form-item>
            <el-form-item :label="$t('writeFreq.scanSamplingTime') + ':'">
              <bfInputNumberV2
                v-model="scanGroup.priSampleTime"
                class="!w-full"
                :disabled="scanGroup.priority1Ch === 0xffff"
                step-strictly
                :min="500"
                :max="7500"
                :step="250"
              />
            </el-form-item>
            <el-form-item :label="$t('writeFreq.residenceTime') + ':'">
              <bfInputNumberV2 v-model="scanGroup.stayTime" class="!w-full" step-strictly :min="0.5" :max="3" :step="0.5" />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <!--漫游-->
      <el-tab-pane v-if="isRConfig" :label="$t('writeFreq.roaming')" name="roamFunction" class="h-full settings-box has-tabs-child flex">
        <div class="basis-1/6 flex justify-center">
          <div class="w-[122px] flex flex-col gap-y-2">
            <div
              v-for="(item, index) in roamList"
              :key="index"
              class="flex justify-center items-center shadow-[inset_0_0_0_2px_rgba(148,204,232,1)] h-[42px] w-[122px]"
              :class="{
                selected: roamGroupIndex === index,
                disToggle: !roamGroup.name,
              }"
              @click="roamGroupIndexLabelClick(item, index)"
            >
              <span class="list-item-name text-[#fff] mr-2" v-text="item.name" />
              <el-button type="danger" icon="delete" size="small" circle @click.stop="deleteRoamGroup(item, index)" />
            </div>
            <bf-button color-type="primary" icon="circle-plus" :disabled="disAddRoamGroup" @click="addOneRoamGroup" />
          </div>
        </div>
        <div v-if="roamList.length" class="basis-5/6">
          <el-form ref="roamGroup" class="grid grid-cols-2 gap-x-3" :model="roamGroup" :rules="roamGroupRules" label-position="top">
            <el-form-item :class="['transfer-wrapper col-span-2 justify-self-center', locale]">
              <bfTransfer
                v-model="roamGroup.memberList"
                :titles="scanningGroupTransferTitles"
                :data="roamGroupChannelList"
                :props="{
                  key: 'value',
                }"
                @change="roamListMembersChange"
              >
                <template #default="{ option }">
                  <span>{{ option.label }}</span>
                </template>
              </bfTransfer>
            </el-form-item>
            <el-form-item>
              <bfCheckbox v-model="roamGroup.config.mainSiteRoaming">
                <span v-text="$t('writeFreq.activeSiteEnable')" />
              </bfCheckbox>
            </el-form-item>
            <el-form-item :label="$t('dialog.name') + ':'" prop="name">
              <bfInput v-model="roamGroup.name" :maxlength="16" />
            </el-form-item>
            <el-form-item :label="$t('writeFreq.siteSearchTimer') + ':'">
              <bfInputNumberV2 v-model="roamGroup.siteSearchTimer" class="!w-full" step-strictly :min="0" :max="255" :step="1" />
            </el-form-item>
            <el-form-item :label="$t('writeFreq.autoSiteSearchTimer') + ':'">
              <bfInputNumberV2 v-model="roamGroup.autoSearchTimer" class="!w-full" step-strictly :min="1" :max="300" />
            </el-form-item>
            <el-form-item :label="$t('dialog.rssiThreshold') + ':'">
              <bfInputNumberV2 v-model="roamGroup.rssi" class="!w-full" step-strictly :min="-120" :max="-80" :step="1" />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <!--电话本-->
      <el-tab-pane :label="$t('dialog.phoneBook')" name="phoneBook" class="h-full settings-box phoneBook-box-container">
        <phoneBook
          :ref="phoneBookTreeId"
          class="settings-box phoneBook-box"
          :treeId="phoneBookTreeId"
          :redrawTree="writerFrequencyTabName === 'phoneBook'"
          @select="selectPhoneBooks"
        />
      </el-tab-pane>
      <!--巡逻系统-->
      <el-tab-pane lazy :label="$t('dialog.patrolSystem')" name="systemFunction" class="h-full patrol-system-box settings-box">
        <el-tabs
          v-model="patrolSystemTabsValue"
          type="border-card"
          class="bf-tab write-freq-component patrol-system-container patrol-system-tabs settings-tabs-box"
        >
          <el-tab-pane :label="$t('dialog.configure')" name="configure" class="h-full">
            <patrolConfig ref="patrolConfig" v-model="patrolConfig" />
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.emergency')" name="emergency" class="h-full">
            <emergencyAlarmConfig ref="emergencyAlarm" v-model="emergencyAlarm" :addressBooks="selectedAddressBook" :showAutoTrackTime="true" hasSelectedItem />
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.trailCtrl')" name="trailCtrl" class="h-full">
            <el-form class="patrol-system-trailCtrl-form" :model="trailCtrl" label-width="95px" label-position="top">
              <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]">
                <el-col :span="8">
                  <el-form-item>
                    <bfCheckbox v-model="trailCtrl.menuConfig.enable">
                      <span v-text="$t('writeFreq.enable')" />
                    </bfCheckbox>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('dialog.pollingTimeSlice') + ':'">
                    <bfInputNumberV2 v-model="trailCtrl.rollTime" class="!w-full" :disabled="trailCtrlDisable" step-strictly :min="10" :max="9995" :step="5" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('dialog.shortestDistance') + ':'">
                    <bfInputNumberV2 v-model="trailCtrl.rollDistant" class="!w-full" :disabled="trailCtrlDisable" step-strictly :min="0" :max="495" :step="5" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--虚拟集群 SDC屏蔽-->
      <el-tab-pane
        v-if="deviceWriteInfo.config.svt"
        :label="$t('writeFreq.virtualCluster')"
        name="virtualCluster"
        class="virtual-cluster-box settings-box"
        lazy
      >
        <!--        <virtualClusterV0-->
        <!--            ref="virtualCluster"-->
        <!--            v-model='virtualCluster'-->
        <!--            :labelPosition='deviceInfoLabelPosition'-->
        <!--            :selectDeviceData='selectDeviceData'-->
        <!--            :selectedAddressBook='selectedAddressBook'-->
        <!--            :channelDataList='channelDataList'-->
        <!--            :version="virtualClusterVersion"-->
        <!--        ></virtualClusterV0>-->
        <el-form ref="virtualCluster" class="virtual-cluster-config-form" :model="virtualCluster" label-position="top" :rules="vcRules">
          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.baseSettings')" />
            </el-divider>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.ownGroup') + ':'">
                <bfInput v-model="vcGroupIdLabel" disabled />
              </el-form-item>
            </el-col>
            <!-- 测机信号间隔 -->
            <el-col :span="8">
              <el-form-item :label="$t('dialog.testMachineSignalInterval', { unit: 'ms' }) + ':'">
                <bfInputNumberV2 v-model.number="virtualCluster.testMachineInterval" class="!w-full" :min="960" :max="18000" :step="120" step-strictly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('dialog.rssiThreshold') + ':'">
                <bfInputNumberV2 v-model.number="virtualClusterRssiValue" class="!w-full" :min="-120" :max="-80" :step="1" step-strictly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('writeFreq.authenticationSecretKey') + ':'" prop="authKey">
                <bfInput v-model="virtualCluster.authKey" :maxlength="32" @blur="authKeyOnchange" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x !w-[calc(100%_-_20px)]" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.scanList')" />
            </el-divider>
            <el-col :xs="24" class="transfer-wrapper">
              <channel-transfer v-model="siteList" :channelList="virtualClusterChIdList" />
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!-- 站点信息 SDC屏蔽 -->
      <el-tab-pane v-if="deviceWriteInfo.config.svt" :label="$t('writeFreq.siteInfo')" name="siteInfo" class="h-full settings-box has-tabs-child flex" lazy>
        <div class="basis-1/6 flex justify-center">
          <div class="w-[122px] flex flex-col gap-y-2">
            <div
              v-for="(item, index) in siteInfoList"
              :key="item.name"
              class="flex justify-center items-center shadow-[inset_0_0_0_2px_rgba(148,204,232,1)] h-[42px] w-[122px]"
              :class="{
                selected: siteInfoIndex === index,
                disToggle: !item.name,
              }"
              @click="siteInfoLabelClick(item, index)"
            >
              <span class="list-item-name text-[#fff] mr-2" v-text="item.name" />
              <el-button type="danger" icon="delete" size="small" circle @click.stop="deleteSiteInfo(item, index)" />
            </div>
            <bf-button color-type="primary" icon="circle-plus" :disabled="siteInfoList.length >= siteInfoLimit" @click="addOneSiteInfo" />
          </div>
        </div>
        <div v-if="siteInfoList.length" class="basis-5/6">
          <SiteInfo_511svt ref="SiteInfo" v-model="siteInfoList[siteInfoIndex]" @update:model-value="siteInfoListChange" />
        </div>
      </el-tab-pane>
    </el-tabs>
    <write-freq-footer
      :is-reading="isReading"
      :is-writing="isWriting"
      :disable-read="disReadBtn"
      :disable-write="disWriteBtn"
      @new-config="newConfig"
      @read-config="readDataConfig"
      @write-config="writeInFrequency"
      @export-config="exportConfig"
      @import-config="importConfig"
    />
  </div>
</template>

<script>
  import { cloneDeep, debounce, merge } from 'lodash'
  import bftree from '@/utils/bftree'
  import bfutil, { DeviceTypes } from '@/utils/bfutil'
  import bfNotify, { messageBox, Types } from '@/utils/notify'
  import bfStorage from '@/utils/storage'
  import validateRules from '@/utils/validateRules'
  import { CallType, getClassInstance, Model, SoftKeyCallType, ButtonKeys } from '@/writingFrequency/interphone/BP620'
  import commonToolMixin from '@/writingFrequency/interphone/commonToolMixin'
  import wfTool from '@/writingFrequency/interphone/tool'
  import { SupportedLang, SupportedLangList } from '@/modules/i18n'
  import {
    filterChannelIdWhenDeviceChanged,
    fixPowerOnPassword,
    fixProgramPassword,
    fixVoiceEncryptKey,
    resetDigitalAlertReplyChannel,
    gpsSettingsQueryCmdInputEvent,
    writeU32LE,
    readU16LE,
  } from '@/writingFrequency/interphone/common'
  import deviceInfo from '@/platform/dataManage/deviceManage/common/deviceInfo'

  import addressBookGroup from '@/platform/dataManage/deviceManage/common/addressBookGroup'
  import { AnalogDigitalCodeDataOptions, encodeSubsonic2String, subtoneIsDigitalCode } from '@/writingFrequency/interphone/subtoneCode'
  import { CmdIO, DataTypes } from '@/writingFrequency/interphone/cmdIO'
  import { encryptedDataTo16Byte } from '@/writingFrequency/interphone/aesEncrypt'
  import WriteFreqFooter from '@/platform/dataManage/deviceManage/common/writeFreqFooter.vue'
  import { VirtualClusterMixin } from '@/writingFrequency/interphone/virtualClusterMixin'
  import { defineAsyncComponent } from 'vue'
  import bfInput from '@/components/bfInput/main'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import bfSelect from '@/components/bfSelect/main'
  import bfCheckbox from '@/components/bfCheckbox/main'
  import bfButton from '@/components/bfButton/main'
  import bfTransfer from '@/components/bfTransfer/main'

  function getTimeZoneOffsetObject(TimeZoneOffset = new Date().getTimezoneOffset()) {
    return {
      timeZoneHour: ((TimeZoneOffset * -1) / 60) | 0,
      timeZoneMinute: Math.abs(TimeZoneOffset) % 60,
    }
  }

  const NetworkModeType = {
    None: 0,
    SDC: 1,
    SVT: 2,
  }
  const timeZoneKeys = ['timeZoneHour', 'timeZoneMinute', 'second', 'minute', 'hour', 'day', 'month', 'year']

  const Password = {
    md5Key: '',
  }
  const DefaultFrequency = {
    value: 400000000,
    label: '400',
  }

  const now = new Date()
  const GeneralSettings = {
    deviceName: '',
    ids: 0,
    repeaterId: 16777215,
    sendPreLastTime: 960,
    offlineGroupCallHungTime: 2000,
    offlineSingleCallHungTime: 2000,
    locale: true,
    baseSettings: {
      rejectUnfamiliarCall: false,
      allowErasing: false,
      allowAddUnfamiliar: false,
      allowDeleteAllRecord: false,
    },
    savePowerMode: 2,
    savePowerDelayTime: 5,
    LEDConfig: {
      disabledAllLED: false,
    },
    soundAndDisplayTip: {
      muteAll: false,
      voiceNotice: true,
      channelFreeNotice: false,
      allowCallInstruction: 2,
      sendSignalSideSound: false,
      decodeSignalSound: false,
    },
    powerInfoAlert: 120,
    soundCtrlLevel: 0,
    soundCtrlDelay: 500,
    stealthSettings: {
      stealthModeEnable: false,
      stealthModeHeadsetMute: false,
    },
    ...getTimeZoneOffsetObject(),
    year: now.getUTCFullYear(),
    month: now.getUTCMonth() + 1,
    day: now.getUTCDate(),
    hour: now.getUTCHours() + getTimeZoneOffsetObject().timeZoneHour,
    minute: now.getUTCMinutes(),
    second: now.getUTCSeconds(),
    gpsSettings: {
      gpsEnable: true,
      beiDouEnable: true,
      glonassEnable: false,
      galileoEnable: false,
      gpsBaudRate: 0,
    },
    recordSettings: {
      enable: false,
    },
    bluetoothSettings: {
      enable: false,
      pttKeepEnable: false,
    },
    denoiseSettings: {
      enable: false,
    },
    alias: '',
  }
  const ButtonDefined = {
    longPressTime: 1000,
    // 长按、短按
    sideKey: [
      // 橙色键
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // 功能1键
      {
        short: ButtonKeys.LONGMONI,
        long: ButtonKeys.NETMODE_CHG,
      },
      // 功能2键
      {
        short: ButtonKeys.MONI,
        long: ButtonKeys.KEYLOCK,
      },
      // PF1
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // PF2
      {
        short: ButtonKeys.SPDCALL3,
        long: ButtonKeys.NONE,
      },
      // PF3
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
    ],
    // 正面6键快捷功能定义
    frontKey: [
      // 确定键
      {
        short: ButtonKeys.MAIN_MENU,
        long: ButtonKeys.NONE,
      },
      // 返回键
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // 方向上键
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // 方向下键
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // P1键
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // P2键
      {
        short: ButtonKeys.BACKTOHOME,
        long: ButtonKeys.NONE,
      },
    ],
    // 单键功能呼叫
    singleKeyCall: [
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
    ],
    // 长按数字键功能呼叫
    longPressNumCallTable: [
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
    ],
    // 预设信道
    defaultChannel: [
      {
        areaId: 0xffff,
        channelId: 0xffff,
      },
      {
        areaId: 0xffff,
        channelId: 0xffff,
      },
      {
        areaId: 0xffff,
        channelId: 0xffff,
      },
      {
        areaId: 0xffff,
        channelId: 0xffff,
      },
    ],
  }
  const EncryptConfig = {
    config: {
      // 加密使能
      encryptEnable: false,
    },
  }
  // 卫星定位设置
  const GpsSettings = {
    baseConfig: {
      enable: false,
      mode: 0xff,
    },
    centerId: 1,
    connectionCount: 8,
    pttCount: 10,
    queryCmd: '',
  }
  const MenuSettings = {
    hangTime: 10,
    lcdHangTime: 10,
    keyboardHangTime: 5,
    baseSetting: {
      menuOff: false,
      chDisplayMode: 2,
      callDisplayMode: 0,
      numberKeyFastDial: true,
      callDirectionEnable: false,
    },
    baseConfig: {
      idShownMinLen: 0,
      bBootDisp: false,
      manualQuickCallType: 0,
      AliasDisp: 0,
    },
    powerOnPwd: '',
    chConfigPwd: '',
    contactConfig: {
      contacts: true,
      contactList: true,
      contactGroup: true,
      manualDialing: true,
      newContact: true,
      groupManagement: true,
    },
    addressSetting: {
      editEnable: true,
      deleteEnable: true,
      groupJoinOrExit: true,
      sendSms: true,
      callTip: true,
      deviceDetect: true,
      remoteMonitor: true,
      deviceActive: true,
    },
    deviceControl: {
      deviceRemoteDeath: true,
    },
    contactGroup: {
      singleDial: true,
      groupDial: true,
      newSingleContact: true,
      newGroupContact: true,
      newGroup: true,
      deleteGroup: true,
      editGroup: true,
    },
    phoneConfig: {
      enable: true,
      phoneList: true,
      manualDial: true,
      newContact: true,
      editContact: true,
      deleteContact: true,
    },
    zoneConfig: {
      enable: true,
    },
    scanConfig: {
      menuEnable: true,
      scanEnable: true,
      editScanList: true,
    },
    roamConfig: {
      menuEnable: true,
      roamEnable: true,
      editRoamList: true,
      lockSite: true,
      manualRoam: true,
    },
    smsConfig: {
      enable: true,
      newSms: true,
      presetSms: true,
      receiveBox: true,
      sendBox: true,
      clearSms: true,
    },
    smsOperation: {
      reply: true,
      forward: true,
      resend: true,
      delete: true,
    },
    callConfig: {
      callRecord: true,
      unReceivedCall: true,
      receivedCall: true,
      callOut: true,
      clearCallRecord: true,
    },
    bluetoothConfig: {
      enable: true,
    },
    recordConfig: {
      menuEnable: false,
      recordEnable: false,
      recordFile: false,
    },
    deviceConfig: {
      menuEnable: true,
      deviceSetting: true,
      offline: true,
      toneTip: true,
      transmitPower: true,
      backLight: true,
      bootInterface: true,
      keyboardLock: true,
    },
    deviceConfig2: {
      ledIndicator: false,
      quieting: true,
      powerOnPassword: false,
      locale: true,
      soundCtrl: true,
      timeSetting: false,
      locate: false,
      workAlone: false,
    },
    deviceConfig3: {
      upendEnable: false,
      hideMode: true,
      encryptEnable: true,
      denoiseEnable: false,
      channelLockEnable: true,
      aliasEnable: true,
    },
    deviceInfo: {
      infoMenuEnable: true,
    },
    channelSetting: {
      channelConfigMenuEnable: true,
      receivingFreq: true,
      transmittingFreq: true,
      channelName: true,
      transmitTimeLimit: true,
      subAudioSetting: true,
      launchContact: true,
      colorCode: true,
    },
    timeSlotSetting: {
      timeSlot: true,
      virtualClusterTimeSlot: true,
      receivingList: true,
    },
  }
  const SignalingSystem = {
    remoteConfig: {
      remoteShutDecodeEnable: true,
      remoteMonitorDecode: false,
      urgentRemoteMonitorDecode: false,
      remoteNoticeDecode: true,
      remoteDetectDecode: true,
      remoteEraseDecode: true,
      remoteStunWakeupDecode: true,
    },
    remoteMonitorDuration: 10,
    remoteAlertCount: 10,
    signalingEncrypt: {
      remoteShutEncryptEnable: false,
      remoteMonitorDecodeEnable: false,
    },
    signalingPwd: '',
  }
  const AlertConfig = {
    aloneWorkEnable: false,
    responseTime: 10,
    remindTime: 10,
    responseOperation: 0,
    upendEnable: false,
    entryDelay: 10,
    quitDelay: 10,
    upendConfig: {
      preRewindTime: 5,
      triggerTilt: 0,
      triggerMode: 0,
    },
  }
  const DigitAlarm = {
    id: 0,
    type: 0,
    mode: 0,
    replyChannel: 0xffff,
    impoliteRetry: 15,
    politeRetry: 5,
    hotMicDuration: 10,
    config: {
      autoSendGps: false,
    },
    name: '',
  }
  const ZoneData = {
    areaId: 0,
    chFlag: 0,
    chIdList: [],
    validFlag: 1,
    areaName: '',
  }

  // 信道数据
  // 信道数据
  const Channel = {
    // 信道 ID
    chId: 0,
    PBAId: 0,
    rxFreq: 400000000,
    txFreq: 400000000,

    chType: 0,
    scanListWrap: 0xff,
    scanList: 0xff,
    scanConfig: {
      listType: 0,
      autoScan: false,
      onlyReceive: false,
      ipSiteConnect: false,
      autoRoam: false,
      allowOfflineSign: false,
    },
    powerConfig: {
      powerType: 2,
      squelchLevel: 3,
    },

    // 数字信道
    colorCode: 0,
    groupList: 0xff,
    defaultAddress: 0xffff,
    timeSlotConfig: {
      timeSlot: 0,
      virtualTimeSlot: 0,
      DCDMEnable: false,
      chSlotAdjust: 0,
      directModeEnable: false,
    },
    voiceConfig: {
      priorityInterrupt: false,
      voicePriority: 0,
      permitConditions: 1,
      singleCallRes: false,
      voiceDuplex: false,
      voiceCallEmbedding: false,
    },
    alertConfig: {
      emergencyAlertTip: false,
      emergencyAlertConfirm: false,
      emergencyCallTip: false,
      unConfirmSingleCall: false,
      smsUdpCompress: false,
      networking: false,
      virtualClusterEnable: false,
      localCall: false,
    },
    emergencySysId: 0xff,
    virtualClusterSiteInfoListId: 0xff,
    encryptConfig: {
      enable: false,
      type: 0,
      algorithm: 0,
      encryptKeyRandom: false,
      decryptKeyRandom: false,
      smsEncrypt: false,
    },
    encryptListId: 0xff,
    aliasConfig: {
      enable: false,
    },

    // 模拟信道
    subsonicDecode: 0xffff,
    subsonicEncode: 0xffff,
    scramble: 3300,
    signalType: 0xff,
    featureListId: 0,
    signalSysId: 0xff,
    noiseType: 0,
    autoResetTime: 20,
    autoResetMode: 0,
    alarmSysId: 0,
    analogChannelConfig: {
      bandwidthFlag: 1,
      beatFreq: false,
      preEmphasis: false,
      companding: false,
      scrambleEnable: false,
      flgBCL: 0,
      tailNoise: true,
    },
    subtoneConfig: {
      ctcssTxDps: 0,
      ctcssRxDps: 0,
    },

    transmissionLimit: 300,
    TOTKeyDelay: 0,
    chName: '',
  }

  const OneScanGroup = {
    scanId: 0,
    memberCount: 1,
    membersList: [0xfffe],
    priority1Ch: 0xffff,
    priority2Ch: 0xffff,
    priSampleTime: 2000,
    stayTime: 3000,
    config: {
      subScanMode: 3,
      reply: true,
      scanTxMode: 0,
    },
    appointTxCh: 0xfffe,
    name: '',
  }
  const OneRoamGroup = {
    roamId: 0,
    config: {
      mainSiteRoaming: false,
      followMainSite: false,
      channelAsScanList: false,
    },
    siteSearchTimer: 0,
    autoSearchTimer: 60,
    rssi: -120,
    roamChCount: 1,
    memberList: [0xfffe],
    name: '',
  }
  // 通讯录群组
  const OneAddressBookGroup = {
    id: 0,
    name: '',
    // 缓存所辖信道
    dmrIdList: [],
    memberList: [],
  }

  // 巡逻系统: 跟踪监控: 启用1/0,轮询时间片s,最小距离m
  const trailCtrl = {
    menuConfig: {
      enable: true,
    },
    rollTime: 300,
    rollDistant: 50,
  }

  // 系统功能紧急报警
  const EmergencyAlarm = {
    alarmConfig: {
      alarmEnable: true,
      alarmType: 1,
    },
    sendCount: 3,
    callingContact: 0xffff,
    autoListenTime: 20,
    autoTrackTime: 30,
  }

  // 默认站点信息参数
  const SiteInfo = {
    id: 0,
    count: 1,
    txFreqList: [bfutil.frequencyMhz2Hz(401)],
    rxFreqList: [bfutil.frequencyMhz2Hz(400)],
    name: 'site info 1',
  }

  // 虚拟集群默认配置
  const VirtualCluster = {
    vcGroupId: 0xffff,
    testMachineInterval: 3600,
    rssiValue: 0,
    siteListMemCount: 1,
    authKey: ''.padEnd(32, 'F'),
    siteList: [0xfffe],
  }

  const pb620Images = import.meta.glob(['@/images/BP620/*'], { eager: true })
  Object.keys(pb620Images).forEach(key => {
    const k = key.split('/').pop()
    pb620Images[k] = pb620Images[key]
  })

  export default {
    name: 'BP620',
    mixins: [commonToolMixin, wfTool, VirtualClusterMixin],
    props: {
      selectedDeviceDmrId: {
        type: String,
        default: '',
      },
      isReading: {
        type: Boolean,
        default: false,
      },
      isWriting: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['update:selectedDeviceDmrId', 'update:isReading', 'update:isWriting'],
    data() {
      return {
        // 系统功能巡查配置
        patrolConfig: {},
        // 系统功能紧急报警,
        emergencyAlarm: cloneDeep(EmergencyAlarm),
        patrolSystemTabsValue: 'configure',
        trailCtrl: cloneDeep(trailCtrl),

        writerFrequencyTabName: 'deviceWriteInfo',
        defaultFrequency: cloneDeep(DefaultFrequency),
        // 设备信息
        deviceWriteInfo: {
          minFrequency: 400,
          maxFrequency: 480,
          config: {
            roam: true,
            bluetooth: true,
            denoise: true,
            locate: true,
            recording: true,
            upendAlarm: true,
            workAlone: true,
            sdc: true,
            svt: true,
          },
        },
        // 缓存身份信息
        identityInfo: {},
        // 编程密码
        passwordInfo: cloneDeep(Password),
        // 菜单设置
        menuSettings: cloneDeep(MenuSettings),
        // 常规设置
        generalSettings: cloneDeep(GeneralSettings),
        chConfigPwdMode: 0,
        // 按键设置
        buttonDefined: cloneDeep(ButtonDefined),
        // 卫星定位
        gpsData: cloneDeep(GpsSettings),
        alertConfig: cloneDeep(AlertConfig),
        // 扫描
        scanConfig: cloneDeep(OneScanGroup.config),
        scanGroup: cloneDeep(OneScanGroup),
        scanGroupIndex: -1,
        scanGroupLimit: 32,
        oneScanGroupLimit: 16,
        scanList: [],
        // 漫游
        roamConfig: cloneDeep(OneRoamGroup.config),
        roamList: [],
        roamGroup: cloneDeep(OneRoamGroup),
        roamGroupIndex: -1,
        roamGroupLimit: 32,
        oneRoamGroupLimit: 16,
        // 信令系统
        signalingSystem: cloneDeep(SignalingSystem),
        // 数字报警
        digitAlarm: cloneDeep(DigitAlarm),
        digitAlarmList: [],
        digitAlarmIndex: -1,
        digitAlarmLimit: 4,
        // 区域列表
        zoneDataList: [],
        zoneDataLimit: 32,
        zoneDataCache: undefined,
        // 信道与区域关系索引
        channelZoneIndex: {},
        oneChannel: cloneDeep(Channel),
        // 信道
        channelDataList: [],
        channelDataTabs: 'areaList',
        // 缓存读取回来的信道，以便在切换选中终端时，将相同ID的信道参数合并
        originChannelDataList: [],
        channelLimit: 16,
        // 信道表格右键菜单事件数据源缓存
        channelCache: undefined,
        // 频率偏移值
        freqOffset: 10,

        selectedAddressBook: [],
        addressBookCache: [],
        originAddressBook: [],
        addrBookTreeId: 'BP620AddressBookTree',

        // 通讯录群组
        addressBookGroup: [],
        currAddressBookGroupId: -1,
        addressBookGroupItem: cloneDeep(OneAddressBookGroup),
        addressBookGroupIndex: -1,
        addressBookGroupLimit: 16,

        // 接收组配置
        rxGroupList: [],
        refReceiveGroup: 'receiveGroup',

        // 电话本
        phoneBookTreeId: 'BP620PhoneBook',
        phoneBook: [],

        // 多个数据的结构写频时，几个写一次
        wfCountLimit: 3,
        availableChannels: {
          areaChannelIndexTable: [],
        },
        // 短信内容
        smsContent: [],
        refSms: 'shortMessage',
        // 信道总数
        totalChannels: 38,
        readCount: 0,
        readWriteFlag: '',
        // 默认的信道配置
        defaultChannelConfig: [],

        // 加密配置
        encryptConfig: cloneDeep(EncryptConfig),
        encryptListLimit: 32,
        encryptList: [],
        encryptARC4List: [],
        encryptAES256List: [],

        // 录音文件列表
        recordList: [],
        // 一个录音文件信息
        oneRecord: {},
        // 批量下载的录音文件id集合
        downloadRecordList: [],
        // 虚拟集群
        virtualCluster: cloneDeep(VirtualCluster),
        virtualClusterVersion: 1,

        //  站点信息
        siteInfoList: [],
        siteInfoIndex: 0,
        siteInfoLimit: 16,
      }
    },
    methods: {
      showBtnDefinePreview() {
        this.$refs.btnDefinePreviewRef.showPreview()
      },
      changeObjKeysVal(objArr, val) {
        objArr.forEach(obj => {
          Object.keys(obj).forEach(key => {
            obj[key] = val
          })
        })
      },
      deviceConfigChange(val) {
        this.menuSettings.deviceConfig2.locale = val
        this.menuSettings.deviceConfig.offline = val
        this.menuSettings.deviceConfig.toneTip = val
        this.menuSettings.deviceConfig.transmitPower = val
        this.menuSettings.deviceConfig.backLight = val
        this.menuSettings.deviceConfig.bootInterface = val
        if (this.menuSettings.powerOnPwd.length === 6) {
          this.menuSettings.deviceConfig2.powerOnPassword = val
        }
        this.menuSettings.deviceConfig.keyboardLock = val
        this.menuSettings.deviceConfig2.ledIndicator = val
        this.menuSettings.deviceConfig2.quieting = val
        this.menuSettings.deviceConfig2.soundCtrl = val
        this.menuSettings.deviceConfig2.locate = val
        this.menuSettings.deviceConfig2.timeSetting = val
        this.menuSettings.deviceConfig3.channelLockEnable = val
        this.menuSettings.deviceConfig3.denoiseEnable = val
        this.menuSettings.deviceConfig3.aliasEnable = val
      },
      recordingChange(val) {
        this.changeObjKeysVal([this.menuSettings.recordConfig], val)
      },
      smsMenuEnableChange(val) {
        this.changeObjKeysVal([this.menuSettings.smsOperation, this.menuSettings.smsConfig], val)
      },
      phoneConfigEnableChange(val) {
        this.changeObjKeysVal([this.menuSettings.phoneConfig], val)
      },
      contactsChange(val) {
        this.changeObjKeysVal([this.menuSettings.contactConfig, this.menuSettings.addressSetting], val)
        this.menuSettings.deviceControl.deviceRemoteDeath = val
        this.menuSettings.contactGroup.newSingleContact = val
        this.menuSettings.contactGroup.newGroupContact = val
        this.menuSettings.contactGroup.newGroup = val
        this.menuSettings.contactGroup.deleteGroup = val
        this.menuSettings.contactGroup.editGroup = val
      },
      channelConfigMenuEnableChange(val) {
        this.menuSettings.channelSetting.receivingFreq = val
        this.menuSettings.channelSetting.transmittingFreq = val
        this.menuSettings.channelSetting.transmitTimeLimit = val
        this.menuSettings.channelSetting.subAudioSetting = val
        this.menuSettings.channelSetting.launchContact = val
        this.menuSettings.channelSetting.colorCode = val
        this.menuSettings.channelSetting.channelName = val
        this.menuSettings.timeSlotSetting.timeSlot = val
        this.menuSettings.timeSlotSetting.receivingList = val
      },
      roamMenuEnableChange(val) {
        this.menuSettings.roamConfig.roamEnable = val
        this.menuSettings.roamConfig.editRoamList = val
        this.menuSettings.roamConfig.lockSite = val
        this.menuSettings.roamConfig.manualRoam = val
      },
      scanMenuEnableChange(val) {
        this.menuSettings.scanConfig.scanEnable = val
        this.menuSettings.scanConfig.editScanList = val
      },
      getRecordList() {
        if (this.recordList.length !== 0) {
          this.recordList = []
        }
        this.sendAuthentication(DataTypes.read, true)
      },
      decode28ByteData(body) {
        // let flag = body[0]
        return body.slice(1)
      },
      decode32ByteData(data) {
        // let head = data.slice(0, 4)
        const body = data.slice(4)
        return this.decode28ByteData(body)
      },
      /**
       * 对读取回来的一条录音文件信息进行解码
       * @param {Uint8Array} dataList
       * @returns {Uint8Array}
       */
      decodeRecordData(dataList) {
        const len = readU16LE(dataList, 0)
        const content = dataList.slice(2, len + 2)
        const count = Math.floor(content.length / 32)
        const result = []
        for (let i = 0; i < count; i++) {
          const arr = content.slice(i * 32, i * 32 + 32)
          const res = this.decode32ByteData(arr)
          result.push(...res)
        }
        return result
      },
      async readRecordData(result = []) {
        const data = await this.read(true, true)
        bfglob.console.log('readRecordData item:', data)
        if (!data) {
          this.readRecordTime++
          if (this.readRecordTime >= 10) {
            return Promise.reject(result)
          }
          try {
            await bfutil.sleep(100)
            return await this.readRecordData(result)
          } catch (e) {
            return Promise.reject(e)
          }
        }

        if (this.checkResDataIsEOT(data) || this.checkResDataIsRECORD_EOT(data)) {
          bfglob.console.log('whileReadAllConfig end', data)
          this.setReadState(false)
          return Promise.resolve(result)
        }

        if (this.checkResDataIsNCK(data)) {
          bfglob.console.warn('NCK:', data, result)
          // 返回NCK指令，结束
          this.setReadState(false)
          // 数据读取异常消息提示
          bfNotify.messageBox(this.$t('msgbox.deviceTerminateReadOpt'), 'warning')

          return Promise.reject('NCK')
        }

        this.answerACK()
        const readBufItem = this.decodeRecordData(data)
        result = result.concat(readBufItem)
        return await this.readRecordData(result)
      },
      // 下载一个录音文件
      async downloadRecordItem(record) {
        const buf = writeU32LE(record.fileIdx)
        const buf16 = encryptedDataTo16Byte(buf)
        const base64Str = CmdIO.aesEncrypt(buf16, this.Model, this.ioPassword)
        this.setTimeoutFunc(DataTypes.read)
        this.write(base64Str)
        this.readRecordTime = 0
        try {
          const bytes = await this.readRecordData()
          // 添加2个字节的头信息, [声码器类型，通道类型]
          const head = [record.recordConfig.vocoderType, record.recordConfig.comType]
          return new Uint8Array([...head, ...bytes])
        } catch (_e) {
          return false
        }
      },
      async downloadRecord() {
        let alreadyDownload = 0
        for (let i = 0; i < this.downloadRecordList.length; i++) {
          const res = await this.downloadRecordItem(this.downloadRecordList[i])
          if (res) {
            bfutil.saveAsFile(this.downloadRecordList[i].fileNum + '_' + this.downloadRecordList[i].startTime, res)
            alreadyDownload++
          } else {
            // 文件下载失败
            bfNotify.messageBox(
              this.$t('msgbox.FailedReadSome', {
                fileName: this.downloadRecordList[i].startTime,
              }),
              'wraning'
            )
          }
        }
        this.answerEOT()
        if (alreadyDownload === this.downloadRecordList.length) {
          // 数据读取成功消息提示
          bfNotify.messageBox(this.$t('msgbox.readSuccess'), 'success')
        } else {
          bfNotify.messageBox(
            this.$t('msgbox.readFileSuccessAndFaile', {
              success: this.alreadyDownload,
              fail: this.downloadRecordList.length - alreadyDownload,
            }),
            'warning'
          )
        }
      },
      beforeDownload(recordList) {
        this.downloadRecordList = recordList
        this.sendAuthentication(DataTypes.V)
      },
      authKeyOnchange() {
        // blur event
        const val = this.virtualCluster.authKey
        this.$refs.virtualCluster?.validateField('authKey', valid => {
          if (!valid) {
            return
          }
          // 表单检验通过后，自动补齐32字节
          if (val.length < 32) {
            this.virtualCluster.authKey = val.padEnd(32, 'F')
          }
        })
      },
      subsonicDecodeChange(val) {
        if (this.isAChannel) {
          this.oneChannel.subsonicDecode = encodeSubsonic2String(val)
        }
      },
      isConnectNetworkingChange(val) {
        this.oneChannel.scanList = 0xff
        if (val) {
          this.oneChannel.scanConfig.autoScan = false
          if (this.oneChannel.alertConfig) {
            this.oneChannel.alertConfig.emergencyAlertTip = false
            this.oneChannel.alertConfig.emergencyAlertConfirm = false
            this.oneChannel.alertConfig.emergencyCallTip = false
          }
          this.oneChannel.emergencySysId = 0xff
          // 联网与虚拟集群只能2选1
          if (this.oneChannel.alertConfig) {
            this.oneChannel.alertConfig.virtualClusterEnable = false
          }
        }
      },
      svtEnableChange(value) {
        if (value) {
          // 联网与虚拟集群只能2选1
          if (this.oneChannel.alertConfig) {
            this.oneChannel.alertConfig.networking = false
          }
          if (this.oneChannel.timeSlotConfig) {
            this.oneChannel.timeSlotConfig.timeSlot = 2
            this.oneChannel.timeSlotConfig.virtualTimeSlot = 0
          }

          // 取消报警相关设置
          if (this.oneChannel.alarmSettings) {
            this.oneChannel.alertConfig.emergencyAlertTip = false
            this.oneChannel.alertConfig.emergencyAlertConfirm = false
            this.oneChannel.alertConfig.emergencyCallTip = false
          }
          this.oneChannel.emergencySysId = 0xff
        } else {
          // 取消选择时，需要将虚拟集群中包含该信道的数据删除
          const virtualCluster = { ...this.virtualCluster }
          const index = virtualCluster.siteList.findIndex(v => v === this.oneChannel.chId)
          if (index !== -1) virtualCluster.siteList.splice(index, 1)
          this.virtualCluster = virtualCluster
        }
      },
      netWorkingModeChange(value) {
        // 不是虚拟集群SVT模式，重置站点信息选项
        if (value !== NetworkModeType.SVT) {
          this.oneChannel.virtualClusterSiteInfoListId = 0xff
        }

        // 不设置联网模式
        if (value === NetworkModeType.None) {
          this.isConnectNetworkingChange(false)
          this.svtEnableChange(false)

          if (this.oneChannel.alertConfig) {
            this.oneChannel.alertConfig.localCall = false
          }
          return
        }

        // SDC模式，以前的联网模式
        if (value === NetworkModeType.SDC) {
          this.isConnectNetworkingChange(true)
          this.svtEnableChange(false)
          return
        }

        // SVT模式，新加的虚拟集群模式
        if (value === NetworkModeType.SVT) {
          this.svtEnableChange(true)
          this.isConnectNetworkingChange(false)
        }
      },
      encryptTypeChange(type) {
        this.oneChannel.encryptConfig.algorithm = 0

        // 类型变更为基础类型，则重置密钥列表
        // 变更为高级类型，则根据算法重置为ARC4或AES256
        if (type === 0) {
          this.oneChannel.encryptListId = this.encryptList[0]?.keyId ?? 0xff
        } else if (this.oneChannel.encryptConfig.algorithm === 0) {
          this.oneChannel.encryptListId = this.encryptARC4List[0]?.keyId ?? 0xff
        } else {
          this.oneChannel.encryptListId = this.encryptAES256List[0]?.keyId ?? 0xff
        }
      },
      zoneChannelListDblclick(row) {
        this.zoneChannelListClick(row)
        this.channelDataTabs = 'chSettings'
      },
      setChannelData(data) {
        this.oneChannel = data
        // 纯模拟信道可自定义参数，为确保参数正确显示，先将参数设置为0,再重置

        if (this.oneChannel.chType === 1) {
          // 纯模拟信道可自定义参数，需要将值转换成字符串显示
          let subsonicDecode = this.oneChannel.subsonicDecode
          let subsonicEncode = this.oneChannel.subsonicEncode
          const codeList = this.subtoneCodeDataList
          if (codeList.filter(item => item.value === subsonicDecode).length === 0 && typeof subsonicDecode === 'number') {
            subsonicDecode = this.decodeSubsonic(subsonicDecode)
          }
          if (codeList.filter(item => item.value === subsonicEncode).length === 0 && typeof subsonicEncode === 'number') {
            subsonicEncode = this.decodeSubsonic(subsonicEncode)
          }

          this.oneChannel.subsonicDecode = 0
          this.oneChannel.subsonicEncode = 0
          this.$nextTick(() => {
            this.oneChannel.subsonicDecode = subsonicDecode
            this.oneChannel.subsonicEncode = subsonicEncode
          })
        }
        this.oneChannel.scanListWrap = (this.oneChannel.scanConfig.listType << 8) + this.oneChannel.scanList
        // if (this.oneChannel.chType === 1) {
        // 纯模拟信道可自定义参数，需要将值转换成字符串显示
        // const codeList = this.subtoneCodeDataList
        // if (this.enableCustomCTCSS) {
        //   if (codeList.filter(item => item.value === subsonicDecode).length === 0 && typeof subsonicDecode ===
        //       'number') {
        //     subsonicDecode = decodeSubsonic(subsonicDecode)
        //   }
        //   if (codeList.filter(item => item.value === subsonicEncode).length === 0 && typeof subsonicEncode ===
        //       'number') {
        //     subsonicEncode = decodeSubsonic(subsonicEncode)
        //   }
        // }
      },
      zoneChannelListClick(row) {
        // if (row.chId === this.channelData.chId) { return }
        const channelDataForm = this.$refs.channelData
        if (!channelDataForm) {
          return
        }
        // 如果信道设置的数据在信道列表中，则需要验证表单通过后再切换信道
        if (this.channelDataList.includes(this.oneChannel)) {
          channelDataForm
            .validate()
            .then(() => {
              this.setChannelData(row)
            })
            .catch(() => {})
        } else {
          // 默认初始化信道，直接赋值以同步数据
          this.setChannelData(row)
        }
      },
      channelTableRowClassName({ rowIndex }) {
        return `channel-row channel-${rowIndex}`
      },
      getChannelById(id) {
        for (let i = 0; i < this.channelDataList.length; i++) {
          const channel = this.channelDataList[i]
          if (channel.chId === id) {
            return channel
          }
        }
        return undefined
      },
      getZoneChannels(chIdList = []) {
        return chIdList
          .map(chId => {
            return this.getChannelById(chId)
          })
          .filter(channel => !!channel)
      },
      zoneTableRowClassName({ row, rowIndex }) {
        const cls = row.chIdList && row.chIdList.length ? 'has-details' : 'no-details'
        return `zone-row zone-${rowIndex} ${cls}`
      },
      scanListWrapChange(val) {
        // 高位字节: listType 低位字节 : 扫描/漫游列表的id
        this.oneChannel.scanConfig.listType = val >>> 8
        this.oneChannel.scanList = val & 0xff
        this.$forceUpdate()
      },
      chTypeChanged(val) {
        // 非数字信道，有亚音频参数，需要重置为默认值
        if (val === 0) {
          this.oneChannel.subsonicDecode = 0xffff
          this.oneChannel.subsonicEncode = 0xffff
        } else if (val >= 1) {
          this.oneChannel.subsonicDecode = 0xffff
          this.oneChannel.subsonicEncode = 0xffff

          // 模拟信道，取消IP站点连接
          this.oneChannel.scanConfig.ipSiteConnect = false
        }
      },
      /**
       * 当站点信息列表删除其中一个站点配置，需要同步检查所有信道使用该配置的参数
       * @param siteInfo 被删除的站点信息
       */
      syncChannelSvtSiteInfoWhenDeleteSiteInfo(siteInfo) {
        for (let i = 0; i < this.channelDataList.length; i++) {
          const channel = this.channelDataList[i]
          if (siteInfo.id !== channel.virtualClusterSiteInfoListId) {
            continue
          }
          channel.virtualClusterSiteInfoListId = 0xff
        }
      },
      /**
       * 站点信息列表参数变更事件，需要检查所有信道的虚拟集群站点配置
       * @param siteInfo 当前编辑的站点信息
       */
      siteInfoListChange(siteInfo) {
        for (let i = 0; i < this.channelDataList.length; i++) {
          const channel = this.channelDataList[i]
          if (siteInfo.id !== channel.virtualClusterSiteInfoListId) {
            continue
          }

          // 当前信道的发射与接收频率
          const txFreq = channel.txFreq
          const rxFreq = channel.rxFreq
          // 判断站点信息中对应的频率是否与当前信道的频率相同，如果不同，重置信道的站点信息配置参数
          const txFreqIndex = siteInfo.txFreqList.findIndex(freq => freq === txFreq)
          if (txFreqIndex === -1 || siteInfo.rxFreqList[txFreqIndex] !== rxFreq) {
            channel.virtualClusterSiteInfoListId = 0xff
          }
        }
      },
      initSiteInfoList() {
        this.siteInfoList = []
        this.siteInfoIndex = 0
        this.addOneSiteInfo()
      },
      newSiteInfo() {
        const idList = this.siteInfoList.map(v => {
          return v.id
        })
        const id = this.getNextId(idList, this.siteInfoLimit)
        const data = cloneDeep(SiteInfo)
        data.id = id
        data.name = `${this.$t('writeFreq.siteInfo')} ${id + 1}`
        return data
      },
      addOneSiteInfo() {
        const data = this.newSiteInfo()
        this.siteInfoList[this.siteInfoList.length] = data
        this.siteInfoIndex = this.siteInfoList.length - 1
      },
      siteInfoLabelClick(data, index) {
        this.siteInfoIndex = index
      },
      deleteSiteInfo(data, index) {
        if (this.siteInfoList.length === 1) {
          return
        }

        this.siteInfoList.splice(index, 1)
        this.siteInfoIndex = index === this.siteInfoList.length ? index - 1 : index

        this.syncChannelSvtSiteInfoWhenDeleteSiteInfo(data)
      },
      gpsSettingsQueryCmdInputEvent,
      aloneWorkTimeChange() {
        // 判断是否要重置提醒时间
        if (this.signalingSystem.aloneWorkRemindTime > this.maxAloneWorkRemindTime) {
          this.signalingSystem.aloneWorkRemindTime = this.maxAloneWorkRemindTime
        }
      },
      fixMd5KeyValue(value) {
        this.passwordInfo.md5Key = fixProgramPassword(value)
      },
      fixSoundEncryptValue(value) {
        this.generalSettings.soundEncryptValue = fixVoiceEncryptKey(value)
      },
      fixPowerOnValue(value) {
        this.menuSettings.powerOnPwd = fixPowerOnPassword(value)
      },
      fixChConfigPwd(value) {
        this.menuSettings.chConfigPwd = fixPowerOnPassword(value)
      },
      passwordModeChange(target, pwdProp, mode) {
        // 禁用或独立设置,都清除密码, 否则为同开机密码
        target[pwdProp] = mode === 2 ? this.menuSettings.powerOnPwd : ''
      },
      getAddressByDmrId(dmrId) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item
          }
        }
        return undefined
      },
      getAddressBookFromCache(addrId) {
        return this.originAddressBook.find(item => item.id === addrId)
      },
      getSmsById(msgId) {
        for (let i = 0; i < this.smsContent.length; i++) {
          const sms = this.smsContent[i]
          if (sms.msgId === msgId) {
            return sms
          }
        }
        return undefined
      },
      resetButtonDefined(target) {
        target.callId = 0xffff
        target.callType = SoftKeyCallType.GROUP
        target.smsId = 0xff

        return target
      },
      detectButtonDefined() {
        const fixedFn = item => {
          if (item.callType !== SoftKeyCallType.MSG) {
            return
          }
          if (this.getSmsById(item.smsId)) {
            return
          }

          // 如果按键定义中的短信ID对应的短信内容已经不存在，则使用第一个短信，
          if (this.smsContent.length > 0) {
            item.smsId = this.smsContent[0].msgId
            return
          }

          // 如果都没有短信数据，则按键功能短信ID重置为默认值
          item.smsId = 0xff

          // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
          const contact = this.getSelectedAddress(item.callId)
          if (!contact) {
            this.resetButtonDefined(item)
            return
          }

          // 重置按键功能类型参数
          if (contact.callType === CallType.SINGLE) {
            item.callType = SoftKeyCallType.SINGLE
          } else {
            item.callType = SoftKeyCallType.GROUP
          }
        }
        this.buttonDefined.singleKeyCall.forEach(item => fixedFn(item))
        this.buttonDefined.longPressNumCallTable.forEach(item => fixedFn(item))
      },
      detectButtonDefinedFromSmsChange() {
        const fixedFn = item => {
          if (item.callType !== SoftKeyCallType.MSG) {
            return
          }
          if (this.getSmsById(item.smsId)) {
            return
          }

          // 如果按键定义中的短信ID对应的短信内容已经不存在，则使用第一个短信，
          if (this.smsContent.length > 0) {
            item.smsId = this.smsContent[0].msgId
            return
          }

          // 如果都没有短信数据，则按键功能短信ID重置为默认值
          item.smsId = 0xff

          // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
          const contact = this.getSelectedAddress(item.callId)
          if (!contact) {
            this.resetButtonDefined(item)
            return
          }

          // 重置按键功能类型参数
          if (contact.callType === CallType.SINGLE) {
            item.callType = SoftKeyCallType.SINGLE
          } else {
            item.callType = SoftKeyCallType.GROUP
          }
        }
        this.buttonDefined.singleKeyCall.forEach(item => fixedFn(item))
        this.buttonDefined.longPressNumCallTable.forEach(item => fixedFn(item))
      },
      selectPhoneBooks(books) {
        this.phoneBook = books
      },
      selectAddressBooks(books) {
        this.addressBookCache = cloneDeep(this.BP620DigitalAddress)
        this.selectedAddressBook = books
        // 通讯录变化时，检测按键定义中单键呼叫功能的设置
        this.detectButtonDefined()
      },
      getNextId(idList, limit = 0xffff) {
        if (!Array.isArray(idList)) {
          return 0
        }

        let id = 0
        while (id < limit) {
          if (!idList.includes(id)) {
            return id
          }
          id++
        }
        return id
      },
      getZoneDataByRid(rid = bfutil.DefOrgRid) {
        for (let i = 0; i < this.zoneDataList.length; i++) {
          const zoneData = this.zoneDataList[i]
          if (zoneData.zoneRid === rid) {
            return zoneData
          }
        }

        return undefined
      },
      newZoneData() {
        const idList = this.zoneDataList.map(v => {
          return v.areaId
        })
        const id = this.getNextId(idList, this.zoneDataLimit)
        const zoneData = cloneDeep(ZoneData)
        zoneData.areaId = id
        zoneData.areaName = `${this.$t('dialog.area')} ${id + 1}`
        return zoneData
      },
      // 按键设置
      getKeyName(index) {
        const names = {
          0: this.$t('writeFreq.orangeButton'),
          1: this.$t('writeFreq.customKey', { name: '1' }),
          2: this.$t('writeFreq.customKey', { name: '2' }),
          3: this.$t('writeFreq.lockKeys.confirmKey'),
          4: this.$t('writeFreq.lockKeys.backKey'),
          5: this.$t('writeFreq.lockKeys.upKey'),
          6: this.$t('writeFreq.lockKeys.downKey'),
          7: this.$t('writeFreq.lockKeys.p1Key'),
          8: this.$t('writeFreq.lockKeys.p2Key'),
          9: 'PF1',
          10: 'PF2',
          11: 'PF3',
        }
        return names[index] || ''
      },
      syncLongPressDefine(row) {
        // 如果短按是紧急模式开启,则长按自动设置为紧急模式关闭
        if (row.short === ButtonKeys.WARNING_ON) {
          row.long = ButtonKeys.WARNING_OFF
        } else if (row.long === ButtonKeys.WARNING_OFF) {
          row.long = ButtonKeys.NONE
        }
      },
      defaultChannelZoneRootIdChange(row, rootId) {
        if (rootId === 0xffff) {
          row.areaId = 0xffff
          row.channelId = 0xffff
        }
      },
      buttonDefinedCallIdChange(row, callId) {
        if (callId === 0xffff) {
          row.callType = CallType.GROUP
          row.smsId = 0xff
        }
      },
      buttonDefinedCallTypeChange(row, callType) {
        if (callType === SoftKeyCallType.MSG) {
          row.smsId = this.smsContent[0]?.msgId ?? 0xff
        } else {
          row.smsId = 0xff
        }
      },

      getOriginChannelById(id) {
        for (let i = 0; i < this.originChannelDataList.length; i++) {
          const channel = this.originChannelDataList[i]
          if (channel.chId === id) {
            return cloneDeep(channel)
          }
        }
        return {}
      },
      newChannel(_chType = 0) {
        const idList = this.channelDataList.map(v => {
          return v.chId
        })
        const id = this.getNextId(idList, this.channelLimit * this.zoneDataLimit)
        const channel = cloneDeep(Channel)
        channel.chId = id
        channel.chName = `${this.$t('dialog.channel')} ${id + 1}`
        // 重置信道默认频率
        const minFreq = this.frequencyMhz2Hz(this.deviceWriteInfo.minFrequency)
        channel.receivingFrequency = minFreq || channel.receivingFrequency
        channel.transmittingFrequency = minFreq || channel.transmittingFrequency

        return channel
      },
      addChannelToList(channel) {
        this.channelDataList[this.channelDataList.length] = channel
      },
      initZoneChannel() {
        // 如果没有合法有效的信道，则向用户提示
        if (!this.selectedChannels.length) {
          this.$nextTick(() => {
            bfNotify.messageBox(this.$t('writeFreq.notHaveValidChannel'), 'error')
          })
          return
        }

        // 遍历终端设置的信道参数，生成写频功能的信道数据
        let isNotSetZone = false
        const channelIdLimit = this.zoneDataLimit * this.channelLimit
        const notSetZones = []
        const zoneConfigCache = {}
        // 区域下辖信道上限
        const countLimit = 16
        const channelOverflow = []
        for (let i = 0; i < this.selectedChannels.length; i++) {
          const item = this.selectedChannels[i]
          const zoneRid = item.zoneRid
          // 跳过没有区域数据的信道，并在遍历结束后，向用户提示
          // 缓存信道区域配置，同一个区域配置只查找一次
          const channelZone = zoneConfigCache[zoneRid] || bfglob.gchannelZone.get(zoneRid)
          if (!channelZone || zoneRid === bfutil.DefOrgRid) {
            isNotSetZone = true
            notSetZones.push(item)
            continue
          }
          zoneConfigCache[zoneRid] = channelZone

          let channel = this.newChannel()
          const chId = item.no - 1
          // 判断信道ID是否超出机型的信道范围
          if (chId >= channelIdLimit) {
            continue
          }
          channel.chId = chId
          channel.chName = `${this.$t('dialog.channel')} ${item.no}`
          // 合并读取的信道参数
          channel = merge(channel, this.getOriginChannelById(chId) || cloneDeep(this.oneChannel))
          channel.groupList = this.getRxGroupId(channel.chId)
          channel.defaultAddress = this.getDefaultAddress(item.sendGroup)
          this.addChannelToList(channel)
          // 将信道ID与区域数据相联，如果没有对应的区域，则先创建区域
          let zoneData = this.getZoneDataByRid(channelZone.rid)
          if (!zoneData) {
            zoneData = this.newZoneData()
            zoneData.areaName = channelZone.zoneTitle
            this.zoneDataList[this.zoneDataList.length] = zoneData
          }
          // 判断是否超出区域上限
          if (zoneData.chIdList.length >= countLimit) {
            if (!channelOverflow.some(item => item === channelZone.zoneTitle)) {
              channelOverflow.push(channelZone.zoneTitle)
            }
            continue
          }

          zoneData.zoneRid = channelZone.rid
          zoneData.chIdList.push(channel.chId)
          zoneData.chFlag = this.setZoneChFlag(zoneData.chIdList)
        }

        // 同步信道设置页参数，需要等待子组件加载完成
        this.$nextTick(() => {
          if (this.channelDataList.length) {
            const firstChannel = this.channelDataList[0]
            this.zoneChannelListClick(firstChannel)
          }
        })

        if (isNotSetZone) {
          let msg = this.$t('writeFreq.channelNotSetArea') + ': '
          msg += notSetZones
            .map(item => {
              return `${this.$t('dialog.channel')} ${item.no}`
            })
            .join(',')
          bfNotify.messageBox(msg, 'warning')
        }
        for (let i = 0; i < channelOverflow.length; i++) {
          ;(zoneTitle => {
            setTimeout(() => {
              const msg = this.$t('writeFreq.maxChannelLimit', {
                zoneTitle,
                count: countLimit,
              })
              bfNotify.messageBox(msg, 'warning')
            }, 0)
          })(channelOverflow[i])
        }
      },

      // 数字报警
      initDigitalAlarm() {
        const digitAlarm = this.newDigitAlarm()
        this.digitAlarm = digitAlarm
        this.asyncDigitWarning({ result: [digitAlarm] })
      },
      digitAlarmLabelClick(data, index) {
        if (!this.digitAlarm.name) {
          return
        }
        this.digitAlarmIndex = index
        this.digitAlarm = data
      },
      deleteDigitAlarm(data, index) {
        if (this.digitAlarmList.length === 1) {
          return
        }

        this.digitAlarmList.splice(index, 1)
        this.digitAlarmIndex = index === this.digitAlarmList.length ? index - 1 : index
        this.digitAlarm = this.digitAlarmList[this.digitAlarmIndex]
      },
      newDigitAlarm() {
        const idList = this.digitAlarmList.map(v => {
          return v.id
        })
        const id = this.getNextId(idList, this.digitAlarmLimit)
        const digitAlarm = cloneDeep(DigitAlarm)
        digitAlarm.id = id
        digitAlarm.name = `${this.$t('writeFreq.system')} ${digitAlarm.id + 1}`
        return digitAlarm
      },
      addOneDigitAlarm() {
        const digitAlarm = this.newDigitAlarm()
        this.digitAlarmList[this.digitAlarmList.length] = digitAlarm
        this.digitAlarm = digitAlarm
        this.digitAlarmIndex = this.digitAlarmList.length - 1
      },

      // 通讯录群组
      initAddressBookGroup() {
        this.addressBookGroup = []
        const addressBookGroupItem = cloneDeep(OneAddressBookGroup)
        this.addressBookGroupItem = addressBookGroupItem
        this.addressBookGroupItem.name = `${this.$t('writeFreq.addressBookGroup')} ${addressBookGroupItem.id + 1}`
        this.asyncAddressBookGroup({ result: [addressBookGroupItem] })
      },
      addressBookGroupLabelClick(data, index) {
        if (!this.addressBookGroupItem.name) {
          return
        }
        this.addressBookGroupIndex = index
        this.addressBookGroupItem = data
        this.currAddressBookGroupId = data.id
      },
      deleteAddressBookGroupItem(data, index) {
        if (this.addressBookGroup.length === 1) {
          return
        }

        this.addressBookGroup.splice(index, 1)
        this.addressBookGroupIndex = index === this.addressBookGroup.length ? index - 1 : index
        this.currAddressBookGroupId = this.addressBookGroup[this.addressBookGroupIndex].id
        this.addressBookGroupItem = this.addressBookGroup[this.addressBookGroupIndex]
      },
      newOneAddressBookGroup() {
        const idList = this.addressBookGroup.map(v => {
          return v.id
        })
        const id = this.getNextId(idList, this.addressBookGroupLimit)
        const addressBookGroup = cloneDeep(OneAddressBookGroup)
        addressBookGroup.id = id
        addressBookGroup.name = `${this.$t('writeFreq.addressBookGroup')} ${addressBookGroup.id + 1}`
        return addressBookGroup
      },
      addOneAddressBookGroup() {
        const addressBookGroup = this.newOneAddressBookGroup()
        this.addressBookGroup.push(addressBookGroup)
        // this.addressBookGroup[this.addressBookGroup.length] = addressBookGroup
        this.addressBookGroupItem = addressBookGroup
        this.currAddressBookGroupId = addressBookGroup.id
        this.addressBookGroupIndex = this.addressBookGroup.length - 1
      },
      // 扫描配置
      scanGroupIndexLabelClick(data, index) {
        if (!this.scanGroup.name) {
          return
        }
        this.scanGroupIndex = index
        this.scanGroup = data
      },
      deleteScanGroup(data, index) {
        if (this.scanList.length === 1) {
          return
        }

        this.scanList.splice(index, 1)
        this.scanGroupIndex = index === this.scanList.length ? index - 1 : index
        this.scanGroup = this.scanList[this.scanGroupIndex]
      },
      addOneScanGroup() {
        const oneScanGroup = this.newScanGroup()
        this.scanList[this.scanList.length] = oneScanGroup
        this.scanGroup = oneScanGroup
        this.scanGroupIndex = this.scanList.length - 1
      },
      newScanGroup() {
        const idList = this.scanList.map(v => {
          return v.scanId
        })
        const id = this.getNextId(idList, this.scanGroupLimit)
        const oneScanGroup = cloneDeep(OneScanGroup)
        oneScanGroup.scanId = id
        oneScanGroup.name = `${this.$t('writeFreq.scanningGroup')} ${oneScanGroup.scanId + 1}`
        return oneScanGroup
      },
      initScanGroup() {
        const oneScanGroup = this.newScanGroup()
        this.scanGroup = oneScanGroup
        this.asyncScanList({ result: [oneScanGroup] })
      },

      // 漫游配置
      roamGroupIndexLabelClick(data, index) {
        if (!this.roamGroup.name) {
          return
        }
        this.roamGroupIndex = index
        this.roamGroup = data
      },
      deleteRoamGroup(data, index) {
        if (this.roamList.length === 1) {
          return
        }

        this.roamList.splice(index, 1)
        this.roamGroupIndex = index === this.roamList.length ? index - 1 : index
        this.roamGroup = this.roamList[this.roamGroupIndex]
      },
      addOneRoamGroup() {
        const oneRoamGroup = this.newRoamGroup()
        this.roamList[this.roamList.length] = oneRoamGroup
        this.roamGroup = oneRoamGroup
        this.roamGroupIndex = this.roamList.length - 1
      },
      newRoamGroup() {
        const idList = this.roamList.map(v => {
          return v.roamId
        })
        const id = this.getNextId(idList, this.roamGroupLimit)
        const oneRoamGroup = cloneDeep(OneRoamGroup)
        oneRoamGroup.roamId = id
        oneRoamGroup.name = `${this.$t('writeFreq.roamingGroup')} ${id + 1}`
        return oneRoamGroup
      },
      initRoamGroup() {
        const oneRoamGroup = this.newRoamGroup()
        this.roamGroup = oneRoamGroup
        this.asyncRoamList({ result: [oneRoamGroup] })
      },
      roamListMembersChange(data) {
        if (data.length > this.oneRoamGroupLimit) {
          this.roamGroup.memberList = data.slice(0, this.oneRoamGroupLimit)
          messageBox(this.$t('writeFreq.fullList'), Types.warning)
        }

        this.roamGroup.memberCount = this.roamGroup.memberList.length
      },

      // 短信初始化
      initSmsData() {
        this.smsContent = []
      },

      // 以默认的配置覆盖当前的配置
      newConfig() {
        // 清除当前选择的终端设备
        this.$emit('update:selectedDeviceDmrId', '')
        this.clearDeviceDataConfig(true)
        // 清除设备频率信息
        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, {
          maxFrequency: 0,
          minFrequency: 0,
        })
        // 常规设置
        this.generalSettings = cloneDeep(GeneralSettings)
        this.$refs.timeZone?.initTimeZone()
        // 按键设置
        this.buttonDefined = cloneDeep(ButtonDefined)
        // 菜单设置
        this.menuSettings = cloneDeep(MenuSettings)
        // 卫星定位
        this.gpsData = cloneDeep(GpsSettings)
        // 信令系统
        this.signalingSystem = cloneDeep(SignalingSystem)
        // 数字报警
        this.digitAlarm = cloneDeep(DigitAlarm)
        this.digitAlarmList = []
        this.digitAlarmIndex = -1
        this.initDigitalAlarm()
        this.initAddressBookGroup()
        // 区域列表
        this.zoneDataList = []
        this.zoneDataCache = undefined
        this.channelZoneIndex = {}
        // 信道设置
        this.channelVersion = 10
        this.oneChannel = cloneDeep(Channel)
        this.channelDataList = []
        // 扫描
        this.scanConfig = cloneDeep(OneScanGroup.config)
        this.scanGroup = cloneDeep(OneScanGroup)
        this.scanList = []
        this.scanGroupIndex = -1
        this.initScanGroup()
        // 漫游
        this.roamConfig = cloneDeep(OneRoamGroup.config)
        this.roamGroup = cloneDeep(OneRoamGroup)
        this.roamList = []
        this.roamGroupIndex = -1
        this.initRoamGroup()
        // 系统功能巡查配置
        this.patrolConfig = {}
        // 系统功能紧急报警,
        this.emergencyAlarm = cloneDeep(EmergencyAlarm)
        // 系统功能自动定位监控
        this.trailCtrl = cloneDeep(trailCtrl)
        // 恢复通讯录、接收组、短信、电话簿默认配置
        bfStorage.removeItem(`fancytree:${bfglob.userInfo.rid}:${this.addrBookTreeId}`)
        this.initRxGroupList()
        this.initSmsData()
        // 录音文件信息列表
        this.recordList = []
        this.downloadRecordList = []
        // 虚拟集群
        this.virtualCluster = cloneDeep(VirtualCluster)
        this.virtualClusterVersion = 1
      },
      exportConfig() {
        // 只导出设备的写频配置，不导出设备的接收组、信道数据、设备的dmrId等
        const dataStructType = [1, 2, 4, 5, 6, 7, 8, 9, 10, 13, 15, 17, 19, 21, 22, 23, 27, 28, 21]
        const jsonData = dataStructType
          .map(type => {
            let config = this.getBeforeEncodeData(type)
            // 删除一些不需要导出的参数
            switch (type) {
              case 4:
                // 将常规设置中的设备dmrId和设备名称去掉
                delete config.deviceName
                delete config.ids
                break
            }
            if (!Array.isArray(config)) {
              config = [config]
            }
            return { [type]: config }
          })
          .reduce((p, c) => {
            return Object.assign(p, c)
          }, {})

        this.exportData(jsonData)
      },
      readDataConfig() {
        if (!this.canRead() || this.isReading) {
          return
        }
        // 开始读取数据提示
        bfNotify.messageBox(this.$t('msgbox.startReading'))
        // if (this.selectedDeviceDmrId) {
        //   this.selectedDeviceDmrId = ''
        // } else {
        //   this.clearDeviceDataConfig()
        // }
        this.$emit('update:selectedDeviceDmrId', '')
        this.clearDeviceDataConfig(true)
        this.sendAuthentication()
      },
      validateAllRules() {
        return new Promise((resolve, reject) => {
          const validateList = [
            {
              ref: 'generalSettings',
              msg: this.$t('writeFreq.generalSettingsFormValidate'),
            },
            {
              ref: 'digitAlarm',
              msg: this.$t('writeFreq.digitalAlarmFormValidate'),
            },
            {
              ref: 'scanGroup',
              msg: this.$t('writeFreq.scanGroupFormValidate'),
            },
            {
              ref: 'virtualCluster',
              msg: this.$t('writeFreq.virtualClusterFormValidate'),
            },
          ]
          // 信道列表中有数据时才需要验证
          if (this.channelDataList.length) {
            validateList.push({
              ref: 'channelV9',
              msg: this.$t('writeFreq.channelDataFormValidate'),
            })
          }
          const iterator = validateList[Symbol.iterator]()

          const validate = item => {
            if (item.done) {
              resolve()
            }
            const { ref, msg } = item.value
            this.formValidate(ref)
              .then(() => {
                validate(iterator.next())
              })
              .catch(() => {
                reject(msg)
              })
          }

          validate(iterator.next())
        })
      },
      writeInFrequency() {
        this.writeDataConfig()
      },
      getBeforeEncodeData(type) {
        let result
        const typeStr = type + ''
        switch (typeStr) {
          // 设备信息
          case '1':
            return cloneDeep(this.deviceWriteInfo)
          // 身份信息
          case '2':
            return cloneDeep(this.identityInfo)
          // 通讯密码
          case '3':
            return cloneDeep(this.passwordInfo)
          // 总体设置
          case '4':
            return cloneDeep(this.generalSettings)
          // 按键设置
          case '5':
            return cloneDeep(this.buttonDefined)
          // 短信
          case '6':
            return cloneDeep(this.smsContent)
          // 加密配置
          case '7':
            return cloneDeep(this.encryptConfig)
          // 基础密钥列表
          case '8':
            return cloneDeep(this.encryptList)
          // ARC4密钥列表
          case '9':
            return cloneDeep(this.encryptARC4List)
          // AES256密钥列表
          case '10':
            return cloneDeep(this.encryptAES256List)
          // GPS
          case '13':
            return cloneDeep(this.gpsData)
          // 菜单配置
          case '15':
            return cloneDeep(this.menuSettings)
          // 信令系统
          case '16':
            return cloneDeep(this.signalingSystem)
          // 报警配置
          case '17':
            return cloneDeep(this.alertConfig)
          // 数字紧急报警
          case '19':
            return cloneDeep(this.digitAlarmList)
          // 数字通讯录
          case '21':
            return cloneDeep(this.selectedAddressBook)
          // 数字通讯录群组
          case '22':
            return cloneDeep(this.addressBookGroup)
          // 电话簿
          case '23':
            return this.phoneBook.filter(item => {
              return !!bfglob.gphoneBook.getDataByIndex(item.phoneNo)
            })
          // 接收组列表
          case '24':
            return this.receiveGroup ? this.receiveGroup.getWriteRxGroupList() : []
          // 区域数据
          case '25':
            return cloneDeep(this.zoneDataList)
          // 信道
          case '26':
            return cloneDeep(this.channelDataList)
          // 扫描列表
          case '27':
            return cloneDeep(this.scanList)
          // 漫游列表
          case '28':
            return cloneDeep(this.roamList)
          // 录音文件
          // case '31':
          // return cloneDeep(this.recordList)
          // 配置
          case '51':
            return cloneDeep(this.patrolConfig)
          // 紧急报警
          case '53':
            return cloneDeep(this.emergencyAlarm)
          // 自动定位监控
          case '54':
            return cloneDeep(this.trailCtrl)
          // 虚拟集群
          case '60':
            return cloneDeep(this.virtualCluster)
          // 站点信息
          case '61':
            return cloneDeep(this.siteInfoList)
        }
        return result
      },

      saveDefaultFrequency(frequency = 400) {
        this.defaultFrequency.value = bfutil.frequencyMhz2Hz(frequency)
        this.defaultFrequency.label = bfutil.frequencyHz2Mhz(this.defaultFrequency.value)
      },
      asyncDeviceWriteInfo(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, res.result[0])
        this.saveDefaultFrequency(this.deviceWriteInfo.minFrequency)
      },
      asyncIdentityInfo(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.identityInfo = merge(this.identityInfo, settings)

        // 显示序列号
        this.deviceWriteInfo['serizeNumber'] = this.identityInfo.serizeNumber
      },
      asyncGeneralSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.generalSettings = merge(this.generalSettings, settings)
      },
      asyncButtonsDefine(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        this.buttonDefined = merge(this.buttonDefined, res.result[0])
      },
      asyncMenuSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.menuSettings = merge(this.menuSettings, settings)
      },
      asyncGpsSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.gpsData = merge(this.gpsData, settings)
      },
      asyncSignalingSystemSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        Object.assign(this.signalingSystem, settings)
      },
      asyncDigitWarning(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          if (item.id === 0) {
            this.digitAlarmList[0] = merge(this.digitAlarmList[0], item)
          } else {
            this.digitAlarmList.push(item)
          }
        })

        if (this.digitAlarmList.length && this.digitAlarmIndex <= 0) {
          this.digitAlarmLabelClick(this.digitAlarmList[0], 0)
        }
      },
      asyncAlertConfigSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.alertConfig = merge(this.alertConfig, settings)
      },
      asyncAddressBookGroup(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        this.currAddressBookGroupId = -1
        res.result.forEach(item => {
          // 关联群组与联系人数据
          this.originAddressBook.forEach(add => {
            item.dmrIdList = item.dmrIdList ?? []
            item.memberList = item.memberList ?? []
            if (add.groupId === item.id) {
              item.dmrIdList.push(add.dmrId)
              item.memberList.push(add.id)
            }
          })
          if (item.id === 0) {
            this.addressBookGroup[0] = merge(this.addressBookGroup[0], item)
          } else {
            this.addressBookGroup.push(item)
          }
        })

        this.$nextTick(() => {
          this.addressBookGroupLabelClick(this.addressBookGroup[0], 0)
        })
      },
      setZoneChFlag(chIdList) {
        if (!Array.isArray(chIdList) || !chIdList.length) {
          return 0
        }

        let val = 0
        let i = 0
        const limit = chIdList.length
        while (i < limit) {
          val += 1 << i++
        }

        return val
      },
      getZoneChIdList(zoneData) {
        if (!zoneData || !Array.isArray(zoneData.chIdList)) {
          return []
        }
        // 每个区域下只有16个信道，信道使用标志右移后按位与0x01,结果为1则启用该信道
        let i = 0
        const limit = zoneData.chIdList.length
        const chIdList = []
        while (i < limit) {
          if ((zoneData.chFlag >> i) & 0x01) {
            chIdList.push(zoneData.chIdList[i])
          }
          i++
        }
        return chIdList
      },
      setZoneDataChIdIndex(zoneData) {
        // 添加信道对应的区域ID索引
        zoneData.chIdList.forEach(chId => {
          this.channelZoneIndex[chId] = zoneData.areaId
        })
      },
      asyncZoneData(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        // 保留区域实际的信道id列表
        res.result.forEach(item => {
          item.chIdList = this.getZoneChIdList(item)
          this.setZoneDataChIdIndex(item)
          this.zoneDataList.push(item)
        })

        // 处理按键定义的预设信道
        const defaultChannel = this.buttonDefined.defaultChannel
        for (let i = 0; i < defaultChannel.length; i++) {
          if (defaultChannel[i].channelId === 0xffff) {
            defaultChannel[i].areaId = 0xffff
            continue
          }
          for (let j = 0; j < this.zoneDataList.length; j++) {
            const index = this.zoneDataList[j].chIdList.findIndex(id => defaultChannel[i].channelId === id)
            if (index !== -1) {
              defaultChannel[i].areaId = this.zoneDataList[j].areaId
              break
            }
          }
        }
      },
      asyncChannelDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        let displayChannelIndex = -1
        res.result.forEach(item => {
          // 合并信道参数
          const index = this.channelDataList.findIndex(channel => channel.chId === item.chId)
          if (index > -1) {
            const channel = this.channelDataList[index]
            this.channelDataList[index] = merge(channel, item)
          } else {
            this.channelDataList.push(item)
          }

          // 标记当前信道设置中相同ID的数据源索引
          if (this.oneChannel.chId === item.chId) {
            displayChannelIndex = index > -1 ? index : this.channelDataList.length
          }
        })

        this.originChannelDataList = cloneDeep(this.channelDataList)
        const firstChannel = this.channelDataList[displayChannelIndex > -1 ? displayChannelIndex : 0]
        this.zoneChannelListClick(firstChannel)
      },
      asyncScanConfig(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.scanConfig = merge(this.scanConfig, settings)
      },
      asyncScanList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          item.membersList = item.membersList.slice(0, item.memberCount)
          if (item.scanId === 0) {
            this.scanList[0] = merge(this.scanList[0], item)
          } else {
            this.scanList.push(item)
          }
        })

        if (this.scanList.length && this.scanGroupIndex <= 0) {
          this.scanGroupIndexLabelClick(this.scanList[0], 0)
        }
      },
      asyncRoamConfig(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.roamConfig = merge(this.roamConfig, settings)
      },
      asyncRoamList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          item.memberList = item.memberList.slice(0, item.memberCount)
          if (item.roamId === 0) {
            this.roamList[0] = merge(this.roamList[0], item)
          } else {
            this.roamList.push(item)
          }
        })

        if (this.roamList.length && this.roamGroupIndex <= 0) {
          this.roamGroupIndexLabelClick(this.roamList[0], 0)
        }
      },
      asyncAddressBook(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        // 同步本地通讯录树
        if (this.addressBookTree) {
          this.addressBookTree.asyncNodeSelectStatus(res.result)
        }

        this.originAddressBook = this.originAddressBook.concat(res.result)
      },
      getOriginAddressBook(id) {
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.id === id) {
            return item
          }
        }
        return undefined
      },
      asyncRxGroup(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 处理读取回来的接收组数据
        if (this.receiveGroup) {
          this.receiveGroup.asyncRxGroup(res.result)
        }
      },
      asyncPatrolConfig(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.patrolConfig = merge(this.patrolConfig, settings)
      },
      asyncEmergencyAlarm(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.emergencyAlarm = merge(this.emergencyAlarm, settings)
      },
      asyncTrailCtrl(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.trailCtrl = merge(this.trailCtrl, settings)
      },
      asyncShortMessage(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        for (let i = 0; i < res.result.length; i++) {
          const sms = res.result[i]
          const index = this.smsContent.findIndex(item => item.msgId === sms.msgId)
          if (index === -1) {
            this.smsContent.push(sms)
          } else {
            this.smsContent[index] = sms
          }
        }
      },
      asyncPhoneBook(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        // 保存本地数据
        for (let i = 0; i < res.result.length; i++) {
          const pb = res.result[i]
          const index = this.phoneBook.findIndex(item => item.phoneId === pb.phoneId)
          if (index === -1) {
            this.phoneBook.push(pb)
          } else {
            this.phoneBook[index] = pb
          }
        }

        if (this.phoneBookTree) {
          this.phoneBookTree.asyncNodeSelectStatus(res.result)
        }
      },
      asyncRecordList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        const record = res.result[0]
        const index = this.recordList.findIndex(item => item.fileIdx === record.fileIdx)

        if (index === -1) {
          this.recordList.push(record)
        } else {
          this.recordList[index] = record
        }
      },
      asyncRecord(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        const recordItem = res.result[0]
        const index = this.oneRecord.findIndex(item => item.fileIdx === recordItem.fileIdx)

        if (index === -1) {
          this.oneRecord.push(recordItem)
        } else {
          this.recordList[index] = recordItem
        }
      },
      asyncLocalConfig(data) {
        // bfglob.console.log('asyncLocalConfig data:', data.type, data)
        switch (data.type) {
          // 单个配置对象
          // "1": "deviceWriteInfo",设备信息
          case 1:
            this.asyncDeviceWriteInfo(data)
            break
          case 2:
            this.asyncIdentityInfo(data)
            break
          // "4": "generalSettings",总体设置
          case 4:
            this.asyncGeneralSettings(data)
            break
          // 5: 按键设置
          case 5:
            this.asyncButtonsDefine(data)
            break
          // "6": "ShortMessage",短信
          case 6:
            this.asyncShortMessage(data)
            break
          // 加密配置
          case 7:
            this.asyncCryptCfg(data)
            break
          // 基础密钥列表
          case 8:
            this.asyncCryptCfgList(data)
            break
          // ARC4密钥列表
          case 9:
            this.asyncCryptCfgArc4List(data)
            break
          // AES256密钥列表
          case 10:
            this.asyncCryptCfgAes256List(data)
            break
          // GPS卫星设置
          case 13:
            this.asyncGpsSettings(data)
            break
          // 菜单设置
          case 15:
            this.asyncMenuSettings(data)
            break
          //  信令系统
          case 16:
            this.asyncSignalingSystemSettings(data)
            break
          // 警报设置
          case 17:
            this.asyncAlertConfigSettings(data)
            break
          // 数字警报
          case 19:
            this.asyncDigitWarning(data)
            break
          // 数字通信录
          case 21:
            this.asyncAddressBook(data)
            break
          // 联系人群组
          case 22:
            this.asyncAddressBookGroup(data)
            break
          // PhoneBook,电话簿
          case 23:
            this.asyncPhoneBook(data)
            break
          // 接收组列表
          case 24:
            this.asyncRxGroup(data)
            break
          // ZoneData
          case 25:
            this.asyncZoneData(data)
            break
          // 信道数据
          case 26:
            if ('version' in (this.structInfo[18] ?? {})) {
              this.channelVersion = this.structInfo[18].version
            }
            this.asyncChannelDataList(data)
            break
          // 扫描列表
          case 27:
            this.asyncScanList(data)
            break
          // 漫游列表
          case 28:
            this.asyncRoamList(data)
            break
          //录音文件
          case 31:
            this.asyncRecordList(data)
            break
          // 巡查系统配置
          case 51:
            this.asyncPatrolConfig(data)
            break
          // 紧急报警
          case 53:
            this.asyncEmergencyAlarm(data)
            break
          // 自动定位监控
          case 54:
            this.asyncTrailCtrl(data)
            break
          // 虚拟集群
          case 60:
            if ('version' in (this.structInfo[58] ?? {})) {
              this.virtualClusterVersion = this.structInfo[58].version
            }
            this.asyncVirtualCluster(data)
            break
          // 站点信息
          case 61:
            this.asyncSiteInfoList(data)
            break
          // 录音信息
          case 432:
            this.asyncRecord(data)
            break
          default:
            bfglob.console.warn('asyncLocalConfig unknown data:', data)
        }
      },
      asyncSiteInfoList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          // 过滤超出频率数量的无效数据
          item.rxFreqList = item.rxFreqList.slice(0, item.count)
          item.txFreqList = item.txFreqList.slice(0, item.count)

          // 合并数据，siteInfoList有一个id为0的默认的参数，如果id是0,则需要覆盖
          if (item.id === 0) {
            const index = this.siteInfoList.findIndex(val => val.id === item.id)
            this.siteInfoList[index] = item
          } else {
            this.siteInfoList.push(item)
          }
        })

        if (this.siteInfoList.length && this.siteInfoIndex <= 0) {
          this.siteInfoLabelClick(this.siteInfoList[0], 0)
        }
      },
      asyncVirtualCluster(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        const config = res.result[0]
        this.virtualCluster = merge(this.virtualCluster, config)
      },
      asyncCryptCfg(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.encryptConfig = merge(this.encryptConfig, settings)
      },
      asyncCryptCfgList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        this.encryptList = this.encryptList.concat(res.result)
      },
      asyncCryptCfgArc4List(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        this.encryptARC4List = this.encryptARC4List.concat(res.result)
      },
      asyncCryptCfgAes256List(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        this.encryptAES256List = this.encryptAES256List.concat(res.result)
      },

      showDeviceInsetOrRemovedMessage() {
        let msg = this.$t('msgbox.usbDeviceInsertSuccess')
        let type = 'success'

        if (this.noDevice) {
          msg = this.$t('msgbox.usbDeviceHasBeenOut')
          type = 'warning'
        }
        bfNotify.messageBox(msg, type)
      },
      resetScanList() {
        this.scanGroupIndex = 0
        this.scanList = []
        this.initScanGroup()
      },
      resetRoamList() {
        this.roamGroupIndex = 0
        this.roamList = []
        this.initRoamGroup()
      },
      resetDigitAlarmList() {
        this.digitAlarmIndex = 0
        this.digitAlarmList = []
        this.initDigitalAlarm()
      },
      resetAddressBookGroup() {
        this.addressBookGroupIndex = 0
        this.addressBookGroup = []
        this.initAddressBookGroup()
      },
      resetZoneDataList() {
        this.zoneDataList = []
      },
      resetChannelDataList() {
        if (this.channelDataList.length) {
          this.originChannelDataList = cloneDeep(this.channelDataList)
        }
        this.channelDataList = []
      },

      // 覆盖常规设置
      resetGeneralSettings(device) {
        this.generalSettings = Object.assign(this.generalSettings, {
          deviceName: device.selfId,
          ids: parseInt(device.dmrId, 16),
        })
      },
      // 接收组列表,将发射组也添加到通讯录中
      initRxGroupList() {
        if (!this.receiveGroup) {
          return Promise.resolve([])
        }
        return this.receiveGroup.initRxGroupList(this.selectedChannels)
      },
      getAddressNameByDmrId(dmrId) {
        // 从读取回来的通讯录中查找对应的dmrId的通讯录名称
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.dmrId === dmrId) {
            return item.name
          }
        }

        // 在通讯录中无法找到数据，则从本地的数据中查找
        const org = bfglob.gorgData.getDataByIndex(dmrId)
        return org ? org.orgShortName : ''
      },
      // 查找通讯录对应的发射组dmrId
      getDefaultAddress(dmrId) {
        const address = this.getSelectedAddressByDmrId(dmrId)
        return address ? address.id : 0xffff
      },
      getSelectedAddressByDmrId(dmrId) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item
          }
        }

        return undefined
      },
      getSelectedAddress(id) {
        return this.BP620DigitalAddress.find(item => item.id === id)
      },
      getDeviceChannelData(device, channelId) {
        for (let i = 0; i < device.channels.length; i++) {
          const item = device.channels[i]
          if (item.no === channelId + 1) {
            return item
          }
        }
        return undefined
      },

      // 同步设备管理数据变化
      updateDeviceData(data) {
        // console.log("updateDeviceData:", data);
        if (!data) {
          return
        }
        if (this.selectedDeviceDmrId === data.oldDmrId) {
          this.$emit('update:selectedDeviceDmrId', data.dmrId)
        }
        if (this.addressBookTree) {
          this.addressBookTree.updateAddrBookTree(data)
        }
      },
      deleteDeviceData(rid, data) {
        // console.log("deleteDeviceData:", data);
        if (!data) {
          return
        }
        if (this.selectedDeviceDmrId === data.dmrId) {
          this.$emit('update:selectedDeviceDmrId', '')
        }
        if (this.addressBookTree) {
          this.addressBookTree.updateAddrBookTree(data)
        }
      },
      globalDeviceChannelsChanged(device) {
        if (!device) {
          return
        }
        if (this.selectedDeviceDmrId === device.dmrId) {
          this.$emit('update:selectedDeviceDmrId', '')
          this.$nextTick(() => {
            this.$emit('update:selectedDeviceDmrId', device.dmrId)
          })
        }
      },

      updateAddrBookTree(_data) {
        if (!this.addressBookTree) {
          return
        }
        this.addressBookTree.treeReload()
      },
      getButtonDefinedLabel(index) {
        switch (index) {
          case 0:
            return this.$t('writeFreq.orangeButton')
          case 1:
            return this.$t('writeFreq.key01')
          case 2:
            return this.$t('writeFreq.key02')
          default:
            return ''
        }
      },

      frequencyHz2Mhz: bfutil.frequencyHz2Mhz,
      frequencyMhz2Hz: bfutil.frequencyMhz2Hz,
      getSoftKeyCallTypeList(data) {
        const list = []
        const address = this.getSelectedAddress(data.callId)
        if (!address) {
          return []
        }
        const hasSms = this.smsContent.length > 0
        if (hasSms) {
          list.push(SoftKeyCallType[SoftKeyCallType.MSG])
        }
        if (address.callType === CallType.SINGLE) {
          list.push(SoftKeyCallType[SoftKeyCallType.SINGLE])
          list.push(SoftKeyCallType[SoftKeyCallType.TIP])
          if (data.optType === SoftKeyCallType.GROUP) {
            data.optType = SoftKeyCallType.SINGLE
          }
        } else {
          list.push(SoftKeyCallType[SoftKeyCallType.GROUP])
        }

        return list.map(key => {
          return {
            label: this.$t(`writeFreq.softKeyCallType.${key}`),
            value: SoftKeyCallType[key],
          }
        })
      },
      getSoftKeyFuncDefine(scope, _typeVal = 0) {
        const $index = scope
        let excludes = []
        if ($index === 0) {
          // 短按，没有紧急模式关闭
          // 长按，没有紧急模式开启
          excludes = [ButtonKeys.WARNING_OFF]
        } else {
          excludes = [ButtonKeys.WARNING_ON]
        }

        // 根据机型选配功能来过滤对应的选项
        // 没有定位
        if (!this.deviceWriteInfo.config.locate) {
          excludes.push(ButtonKeys.GPS_POSITION_SWITCH)
        }
        // 没有录音
        if (!this.deviceWriteInfo.config.recording) {
          excludes.push(ButtonKeys.RECORD_SWITCH)
        }
        // 没有蓝牙
        if (!this.deviceWriteInfo.config.bluetooth) {
          excludes.push(ButtonKeys.SK_BT_SWITCH, ButtonKeys.SK_BT_SEARCH_AUTO_CONNECT)
        }
        // // 没有SDC
        // if (!this.deviceWriteInfo.config.sdc) { }
        // // 没有SVT
        // if (!this.deviceWriteInfo.config.svt) { }

        return this.softKeyFuncDefine.filter(opt => {
          return !excludes.includes(opt.value)
        })
      },
      scanListMembersChange(data) {
        // 设置选中的信道时，最大为16个
        if (data.length > this.oneScanGroupLimit) {
          this.scanGroup.membersList = data.slice(0, this.oneScanGroupLimit)
          messageBox(this.$t('writeFreq.fullList'), Types.warning)
        }

        this.scanGroup.memberCount = this.scanGroup.membersList.length
      },
      filterChannelId() {
        const channelIdList = this.currentChannelIdList
        this.scanList = this.scanList.map(item => {
          item.membersList = filterChannelIdWhenDeviceChanged(channelIdList, item.membersList)
          // item.channelCount = item.membersList.length
          item.memberCount = item.membersList.length
          item.priority1Ch = 65535
          item.priority2Ch = 65535
          return item
        })
      },
      filterRoamListChannelId() {
        const channelIdList = this.currentChannelIdList
        this.roamList = this.roamList.map(item => {
          item.memberList = filterChannelIdWhenDeviceChanged(channelIdList, item.memberList)
          item.memberCount = item.memberList.length
          return item
        })
      },
      resetDigitalAlarmReplyChannel() {
        const channelIdList = this.currentChannelIdList
        this.digitAlarmList = this.digitAlarmList.map(data => {
          data.replyChannel = resetDigitalAlertReplyChannel(channelIdList, data.replyChannel)
          return data
        })
      },
      filterButtonDefine() {
        // 信道区域数据里面找不到和预设信道相同区域id的区域，或找不到相同信道id的信道
        for (let i = 0; i < this.buttonDefined.defaultChannel.length; i++) {
          const zone = this.zoneDataList.find(item => {
            const sameZone = item.areaId === this.buttonDefined.defaultChannel[i].areaId
            if (!sameZone) {
              return false
            }
            const chIdIndex = item.chIdList.findIndex(id => id === this.buttonDefined.defaultChannel[i].channelId)
            return chIdIndex !== -1
          })
          if (!zone) {
            this.buttonDefined.defaultChannel[i].areaId = 0xffff
            this.buttonDefined.defaultChannel[i].channelId = 0xffff
          }
        }
        // 长按数字键盘功能呼叫过滤
        for (let i = 0; i < this.buttonDefined.singleKeyCall.length; i++) {
          const addr = this.selectedAddressBook.find(addressBook => addressBook.id === this.buttonDefined.singleKeyCall[i].callId)
          if (!addr) {
            this.buttonDefined.singleKeyCall[i].callMode = 0
            this.buttonDefined.singleKeyCall[i].callId = 65535
            this.buttonDefined.singleKeyCall[i].callType = 1
            this.buttonDefined.singleKeyCall[i].smsId = 255
          }
        }
        for (let i = 0; i < this.buttonDefined.longPressNumCallTable.length; i++) {
          const addr = this.selectedAddressBook.find(addressBook => addressBook.id === this.buttonDefined.longPressNumCallTable[i].callId)
          if (!addr) {
            this.buttonDefined.longPressNumCallTable[i].callMode = 0
            this.buttonDefined.longPressNumCallTable[i].callId = 65535
            this.buttonDefined.longPressNumCallTable[i].callType = 1
            this.buttonDefined.longPressNumCallTable[i].smsId = 255
          }
        }
      },
      async selectDeviceDataChanged(device) {
        // 常规设置
        this.resetGeneralSettings(device)
        // 接收组
        await this.initRxGroupList()
        // 信道
        this.initZoneChannel()
        // 扫描组过滤不存在的信道ID
        this.filterChannelId()
        // 漫游组过滤不存在的信道ID
        this.filterRoamListChannelId()
        // 数字报警重置回复信道参数
        this.resetDigitalAlarmReplyChannel()
        // 过滤按键定义中不存在本系统的数据
        this.filterButtonDefine()
        this.originAddressBook = []
        // 处理虚拟群集的归属组
        //所属归属组，从组呼联系人选择, 所以联系人中必须得选中归属组
        if (device.deviceType === DeviceTypes.VirtualClusterDevice) {
          const devGroup = device.devGroup
          // 归属组在通讯录上设置选中且禁用状态
          bfglob.emit(`${this.addrBookTreeId}:unselectableNode`, [devGroup], () => {
            // 等待通讯录渲染完成
            setTimeout(() => {
              this.$nextTick(() => {
                const book = this.selectedAddressBook.find(item => item.dmrId === devGroup)
                // 设置归属组在通讯录上的ID
                if (book) {
                  this.virtualCluster.vcGroupId = book.id
                }
              })
            }, 0)
          })
        }
      },
      // 清除通讯录被禁用的状态
      clearTreeNodeUnselectable() {
        // 清除通讯录被禁用和非系统通讯数据的节点
        this.addressBookTree && this.addressBookTree.removeNotInSystemNodes()
        this.selectedAddressBook.forEach(item => {
          bftree.nodeUnselectable(this.addrBookTreeId, item.nodeKey)
        })
      },
      clearPrivateConfig() {
        // 清除常规设置的设备名称
        this.generalSettings.deviceName = ''
        this.generalSettings.ids = 0
        // 接收组
        this.receiveGroup && this.receiveGroup.resetRxGroupList()
        // 区域
        this.resetZoneDataList()
        // 信道
        this.resetChannelDataList()
        this.clearTreeNodeUnselectable()
        if (this.phoneBookTree) {
          this.phoneBookTree.removeNotInSystemNodes()
        }
      },
      // cleanAll 标记是否清除全部配置
      async clearDeviceDataConfig(cleanAll = false) {
        // 必须清除的数据，接收组、区域、信道等私有数据，包括一些标记参数
        this.clearPrivateConfig()

        // 密钥
        this.encryptList = []
        this.encryptARC4List = []
        this.encryptAES256List = []

        this.resetScanList()
        this.resetRoamList()
        this.resetDigitAlarmList()
        this.resetAddressBookGroup()
        this.initSiteInfoList()

        // 可选的清除数据，常规设置、菜单、按键定义、警报、通讯录、电话本、短信等通用数据
        if (!cleanAll) {
          return
        }
        this.generalSettings = cloneDeep(GeneralSettings)
        this.menuSettings = cloneDeep(MenuSettings)
        this.buttonDefined = cloneDeep(ButtonDefined)
        this.alertConfig = cloneDeep(AlertConfig)
        this.gpsData = cloneDeep(GpsSettings)
        this.signalingSystem = cloneDeep(SignalingSystem)
        this.originAddressBook = []
        this.selectedAddressBook = []
        this.addressBookCache = []
        this.addressBookTree.treeReload(true)
        this.phoneBook = []
        this.phoneBookTree && this.phoneBookTree.treeReload(true)
      },
    },
    computed: {
      btnDefinePreviewUrl() {
        const imageLang = this.$i18n.locale === SupportedLang.zhCN ? this.$i18n.locale : SupportedLang.enUS
        const imagePath = `btnDefine.${imageLang}.jpg`
        return pb620Images[imagePath]?.default
      },
      scanTxModeList() {
        // 扫描发射模式	0 当前信道(默认值) 1 最后活动信道 2 指定信道
        return [
          { label: this.$t('dialog.currentChannel'), value: 0 },
          { label: this.$t('writeFreq.lastActivityChannel'), value: 1 },
          { label: this.$t('writeFreq.specifiedChannel'), value: 2 },
        ]
      },
      confirmedDataSingleCall: {
        get() {
          return !this.oneChannel.alertConfig.unConfirmSingleCall
        },
        set() {
          this.oneChannel.alertConfig.unConfirmSingleCall = !this.oneChannel.alertConfig.unConfirmSingleCall
        },
      },
      isDeviceConfigMenuEnable() {
        return !this.menuSettings.deviceConfig.deviceSetting
      },
      isRecordMenuEnable() {
        return !this.menuSettings.recordConfig.menuEnable
      },
      isSmsMenuEnable() {
        return !this.menuSettings.smsConfig.enable
      },
      isPhoneConfigMenuEnable() {
        return !this.menuSettings.phoneConfig.enable
      },
      isContactMenuEnable() {
        return !this.menuSettings.contactConfig.contacts
      },
      channelConfigMenuEnable() {
        return !this.menuSettings.channelSetting.channelConfigMenuEnable
      },
      menuRoamDisable() {
        return !this.menuSettings.roamConfig.menuEnable
      },
      menuScanDisable() {
        return !this.menuSettings.scanConfig.menuEnable
      },
      sideKeyAndFrontKey() {
        return [
          this.buttonDefined.sideKey[0],
          this.buttonDefined.sideKey[1],
          this.buttonDefined.sideKey[2],
          ...this.buttonDefined.frontKey,
          this.buttonDefined.sideKey[3],
          this.buttonDefined.sideKey[4],
          this.buttonDefined.sideKey[5],
        ]
      },
      BP620DigitalAddress() {
        const addressBooks = []
        this.originAddressBook.forEach(item => {
          const index = this.selectedAddressBook.findIndex(i => item.dmrId === i.dmrId)
          if (index === -1) {
            addressBooks.push(item)
          }
        })
        return this.selectedAddressBook.concat(addressBooks)
      },
      availableContactList() {
        // 通讯录候选列表不能有其他群组已选中的数据
        return this.selectedAddressBook
          .filter(item => {
            return item.groupId === 0xffff || item.groupId === this.addressBookGroupItem.id
          })
          .map(item => {
            return {
              label: item.name,
              value: item.id,
            }
          })
      },
      titles() {
        return [this.$t('writeFreq.addressGroup.ungroupedContact'), this.$t('writeFreq.addressGroup.selectedContact')]
      },
      virtualClusterChIdList() {
        // 虚拟集群扫描列表的可用信道只允许包含勾选了”虚拟集群”的数字信道
        return this.channelDataList
          .filter(ch => {
            const isVirtualCluster = ch.alertConfig?.virtualClusterEnable ?? false
            return ch.chType === 0 && isVirtualCluster
          })
          .map(ch => ({
            value: ch.chId,
            label: ch.chName,
          }))
      },
      siteList: {
        get() {
          const list = this.virtualCluster.siteList
          const siteListMemCount = this.virtualCluster.siteListMemCount
          if (list.length > siteListMemCount) {
            return list.slice(0, siteListMemCount)
          }
          return list
        },
        set(list) {
          this.virtualCluster.siteListMemCount = list.length
          this.virtualCluster.siteList = list
        },
      },
      virtualClusterRssiValue: {
        get() {
          return this.virtualCluster.rssiValue * -1
        },
        set(v) {
          this.virtualCluster.rssiValue = v * -1
        },
      },
      vcRules() {
        return {
          authKey: [
            {
              validator: function (rule, value, callback) {
                // 只能是大写的16进制字符
                const reg = /^[0-9A-F]+$/
                // 可以为空
                if (!value) {
                  callback()
                } else if (!reg.test(value)) {
                  const errorMsg = new Error('0-9, A-F')
                  callback(errorMsg)
                } else {
                  callback()
                }
              },
              trigger: 'change',
            },
          ],
        }
      },
      gpsMode: {
        get() {
          if (!this.gpsData.baseConfig.enable) {
            return 0xff
          }

          return this.gpsData.baseConfig.mode
        },
        set(val) {
          // 关闭GPS
          if (val === 0xff) {
            this.gpsData.baseConfig.enable = false
            this.gpsData.baseConfig.mode = 0
          } else {
            this.gpsData.baseConfig.enable = true
            this.gpsData.baseConfig.mode = val
          }
        },
      },
      subsonicDecodeIsDigitalCode() {
        if (this.oneChannel.subsonicDecode === 0xffff) {
          return true
        }
        return subtoneIsDigitalCode(this.oneChannel.subsonicDecode)
      },
      channelEnableEncrypt() {
        return this.encryptEnable && (this.oneChannel.encryptConfig?.enable ?? false)
      },
      enableAdvancedEncryption() {
        return this.channelEnableEncrypt && this.oneChannel.encryptConfig?.type === 1
      },
      SVTSiteInfoList() {
        const rxFreq = this.oneChannel.rxFreq
        const txFreq = this.oneChannel.txFreq
        const list = this.siteInfoList
          .filter(item => {
            // 判断站点信息中对应的频率是否与当前信道的频率相同，如果不同，重置信道的站点信息配置参数
            const txFreqIndex = item.txFreqList.findIndex(freq => freq === txFreq)
            return txFreqIndex > -1 && item.rxFreqList[txFreqIndex] === rxFreq
          })
          .map(item => {
            return {
              label: item.name,
              value: item.id,
            }
          })
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
          ...list,
        ]
      },
      isSvtEnable() {
        return this.oneChannel.alertConfig?.virtualClusterEnable ?? false
      },
      networkingModeList() {
        const list = [
          {
            label: this.$t('dialog.nothing'),
            value: NetworkModeType.None,
          },
        ]

        if (this.deviceWriteInfo.config.sdc) {
          list.push({
            label: 'SDC',
            value: NetworkModeType.SDC,
          })
        }

        if (!this.sameFreq && this.deviceWriteInfo.config.svt) {
          list.push({
            label: 'SVT',
            value: NetworkModeType.SVT,
          })
        }
        return list
      },
      networkingMode: {
        get() {
          if (!this.oneChannel.alertConfig?.networking && !this.oneChannel.alertConfig?.virtualClusterEnable) {
            return 0
          }

          if (this.oneChannel.alertConfig?.networking) {
            return 1
          }

          if (this.oneChannel.alertConfig?.virtualClusterEnable && !this.oneChannel.alertConfig?.networking) {
            return 2
          }
          return 0
        },
        set(value) {
          if (!this.oneChannel.alertConfig) {
            return
          }

          switch (value) {
            case 0:
              this.oneChannel.alertConfig.networking = false
              this.oneChannel.alertConfig.virtualClusterEnable = false
              break
            case 1:
              this.oneChannel.alertConfig.networking = true
              this.oneChannel.alertConfig.virtualClusterEnable = false
              break
            case 2:
              this.oneChannel.alertConfig.virtualClusterEnable = true
              this.oneChannel.alertConfig.networking = false
              break
          }
        },
      },
      subtoneCodeDataList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        /**
         * 默认的亚音，信道类型为key，亚音编码列表为值
         * @type {{[key:number|string]: Array<any>>}}
         */
        const SubtoneCodeData = {
          0: def,
          // 1: def.concat(AnalogCodeDataOptions),
          // BP620从TD818SVT机型派生，模拟信道亚音编解码采用C6000版本
          1: def.concat(AnalogDigitalCodeDataOptions),
          // 2、3信道类型亚音码
          default: def.concat(AnalogDigitalCodeDataOptions),
        }

        return SubtoneCodeData[this.oneChannel.chType] ?? SubtoneCodeData.default
      },
      throughEnable() {
        return this.oneChannel.timeSlotConfig.DCDMEnable
      },
      encryptKeyList() {
        // 密钥列表序号，根据加密类型不同选择不同密钥列表

        const defaultList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]

        // 基础类型：对应基础密钥列表
        if (this.oneChannel.encryptConfig.type === 0) {
          return defaultList.concat(
            this.encryptList.map(item => {
              return {
                label: item.name,
                value: item.id,
              }
            })
          )
        }

        // 高级类型：ARC4对应ARC4密钥列表；AES256对应AES256列表
        if (this.oneChannel.encryptConfig.algorithm === 0) {
          return defaultList.concat(
            this.encryptARC4List.map(item => {
              return {
                label: item.name,
                value: item.id,
              }
            })
          )
        }

        return defaultList.concat(
          this.encryptAES256List.map(item => {
            return {
              label: item.name,
              value: item.id,
            }
          })
        )
      },
      algorithmList() {
        // 加密算法，
        // 基础类型：0 异或，1 增强异或，2 ARC4，3 AES256，default=0
        // 高级类型：0 ARC4，1 AES256
        if (this.oneChannel.encryptConfig.type === 0) {
          return [
            {
              label: this.$t('writeFreq.xor'),
              value: 0,
            },
            {
              label: this.$t('writeFreq.enhancedXor'),
              value: 1,
            },
            {
              label: 'ARC4',
              value: 2,
            },
            {
              label: 'AES256',
              value: 3,
            },
          ]
        }

        return [
          {
            label: 'ARC4',
            value: 0,
          },
          {
            label: 'AES256',
            value: 1,
          },
        ]
      },
      encryptTypeList() {
        // 加密类型，0 基础，1 高级 default = 0
        return [
          {
            label: this.$t('writeFreq.base'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.advanced'),
            value: 1,
          },
        ]
      },
      disEncryption() {
        return this.disEncryptConfigEnable || !this.oneChannel.encryptConfig.enable
      },
      disEncryptConfigEnable() {
        return !this.encryptConfig.config.encryptEnable
      },
      sameFreq() {
        return this.oneChannel.rxFreq === this.oneChannel.txFreq
      },
      disAutoRoam() {
        return !this.oneChannel.scanConfig.ipSiteConnect || this.oneChannel.scanList === 0xff
      },
      disAutoScan() {
        return this.isConnectNetworking || this.oneChannel.scanConfig.ipSiteConnect || this.oneChannel.scanList === 0xff
      },
      disableScanList() {
        if (this.oneChannel.scanConfig.ipSiteConnect) {
          return false
        }
        if (this.networkingMode !== 0) {
          return true
        }

        return this.isConnectNetworking
      },
      isDChannel() {
        return this.oneChannel.chType === 0
      },
      isAChannel() {
        return this.oneChannel.chType === 1
      },
      channelRules() {
        // const defFreq = this.defaultFrequency
        // const minFrequency = this.deviceWriteInfo.minFrequency
        // const maxFrequency = this.deviceWriteInfo.maxFrequency
        // const min = minFrequency ? bfutil.frequencyMhz2Hz(minFrequency) : defFreq.value
        // const max = maxFrequency ? bfutil.frequencyMhz2Hz(maxFrequency) : defFreq.value + 80000000
        // const smg = `${minFrequency || bfutil.frequencyHz2Mhz(defFreq.value) || 0}~${maxFrequency ||
        // bfutil.frequencyHz2Mhz(defFreq.value + 80000000)}`
        // const frequency = [
        //   validateRules.required(['blur']),
        //   validateRules.range(['blur'], min, max, smg)
        // ]
        return {
          chName: [validateRules.required(['blur'])],
          // rxFreq: frequency,
          // txFreq: frequency
        }
      },
      trailCtrlDisable() {
        return !this.trailCtrl.menuConfig.enable
      },
      priority2ChList() {
        // 如果第一优先信道已经选中的信道，则不可选
        return this.nothingList.concat(
          [...this.theSelectedList, ...this.selectedChannelList].filter(item => {
            return item.value !== this.scanGroup.priority1Ch
          })
        )
      },
      noPriority1Ch() {
        return this.scanGroup.priority1Ch === 0xffff
      },
      nothingList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
      },
      theSelectedList() {
        return [
          {
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
          },
        ]
      },
      scanChannelList() {
        return this.channelDataList.map(channel => {
          return {
            label: channel.chName,
            value: channel.chId,
          }
        })
      },
      selectedChannelList() {
        this.scanGroup.memberCount
        return this.scanChannelList.filter(item => this.scanGroup.membersList.includes(item.value))
      },
      priority1ChList() {
        // 如果第二优先信道已经选中的信道，则不可选
        return this.nothingList.concat(
          [...this.theSelectedList, ...this.selectedChannelList].filter(item => {
            return item.value !== this.scanGroup.priority2Ch
          })
        )
      },
      manualQuickCallList() {
        // 手动快捷呼叫 呼叫方式：默认单呼0、默认组呼1、仅组呼2、仅单呼3；默认0
        return [
          {
            label: this.$t('writeFreq.defaultPriCall'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.DefaultGroupCall'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.onlyGroupCall'),
            value: 2,
          },
          {
            label: this.$t('writeFreq.onlySingleCall'),
            value: 3,
          },
        ]
      },
      callDisplayModeList() {
        // 呼叫显示模式，0 显示名字，1 显示号码，2 显示名字和号码，默认2
        return [
          {
            label: this.$t('writeFreq.displayName'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.displayNumber'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.displayNameAndNumber'),
            value: 2,
          },
        ]
      },
      idShownMinLen: {
        get() {
          return this.menuSettings.baseConfig.idShownMinLen + 1
        },
        set(value) {
          this.menuSettings.baseConfig.idShownMinLen = value - 1
        },
      },
      buttonDefineChannelList() {
        const emptyList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        // zoneId => []
        const zoneCache = {
          0xffff: emptyList,
        }
        this.zoneDataList.forEach(item => {
          zoneCache[item.areaId] = item.chIdList.map(chId => {
            return {
              label: this.channelDataList.find(channel => channel.chId === chId)?.chName,
              value: chId,
              areaId: item.areaId,
            }
          })
        })
        return zoneCache
      },
      buttonDefineZoneRootList() {
        const defaultList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        return defaultList.concat(
          this.zoneDataList.map(item => {
            return {
              label: item.areaName,
              value: item.areaId,
            }
          })
        )
      },
      passwordModeList() {
        const baseList = [
          {
            label: this.$t('writeFreq.disable'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.independentSettings'),
            value: 1,
          },
        ]

        // 6位有效密码
        if (this.menuSettings.powerOnPwd.length < 6) return baseList

        return baseList.concat([
          {
            label: this.$t('writeFreq.samePowerPassword'),
            value: 2,
          },
        ])
      },
      muteAll() {
        return this.generalSettings.soundAndDisplayTip.muteAll
      },
      timeZone: {
        get() {
          return timeZoneKeys
            .map(key => {
              return { [key]: this.generalSettings[key] }
            })
            .reduce((p, c) => {
              return Object.assign(p, c)
            }, {})
        },
        set(value) {
          timeZoneKeys.forEach(key => {
            this.generalSettings[key] = value[key]
          })
        },
      },
      encryptListActiveWidth() {
        return this.isFR ? '120px' : '100px'
      },
      encryptEnable() {
        return this.encryptConfig.config.enable
      },
      currentChannelIdList() {
        return this.channelDataList.map(item => item.chId)
      },
      dmrIdLabel() {
        const intDmrId = this.generalSettings.ids
        if (intDmrId === 0) {
          return ''
        }
        const dmrId = bfutil.int32Dmrid2Hex(intDmrId)
        return dmrId ? ` ${dmrId} / ${intDmrId}` : ''
      },
      isEn() {
        return this.$i18n.locale === 'en'
      },
      fullscreen() {
        return this.bfmaxi ? true : this.$root.layoutLevel === 0
      },
      btnGroupType() {
        return 'small'
      },
      hasFreqRange() {
        return this.deviceWriteInfo.maxFrequency > 0 && this.deviceWriteInfo.minFrequency > 0
      },
      disWriteBtn() {
        return this.disReadBtn || !this.selectedDeviceDmrId || !this.hasFreqRange || this.selectedChannels.length === 0
      },
      disReadBtn() {
        return this.noQWebServer || this.noDevice || this.isWriting || this.isReading
      },
      noQWebServer() {
        return !this.QWebServer
      },
      channelListColumn() {
        return [
          {
            prop: 'channelId',
            label: this.$t('dialog.chId'),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '100px',
          },
          {
            prop: 'channelName',
            label: this.$t('dialog.chName'),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '100px',
          },
          {
            prop: 'recvFreq1',
            label: this.$t('dialog.receiveNumber', { num: 1 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'sendFreq1',
            label: this.$t('dialog.emissionNumber', { num: 1 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'recvFreq2',
            label: this.$t('dialog.receiveNumber', { num: 2 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'sendFreq2',
            label: this.$t('dialog.emissionNumber', { num: 2 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '220px' : '120px',
          },
          {
            prop: 'recvFreq3',
            label: this.$t('dialog.receiveNumber', { num: 3 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'sendFreq3',
            label: this.$t('dialog.emissionNumber', { num: 3 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
        ]
      },
      generalSettingsRules() {
        const pwdRule = [validateRules.mustLength(['change', 'blur'], 6), validateRules.mustNumber(['change', 'blur'])]

        return {
          deviceName: [validateRules.required(), validateRules.maxLen(['change', 'blur'], 16)],
          repeaterId: [validateRules.required(), validateRules.mustNumber(), validateRules.range(['blur'], 1, 16777215, '1~16777215')],
          channelConfigPassword: pwdRule,
        }
      },
      digitAlarmRules() {
        return {
          name: [validateRules.required(['blur', 'change'])],
        }
      },
      addressBookGroupRules() {
        return {
          name: [validateRules.required(['blur', 'change'])],
        }
      },
      scanGroupRules() {
        return {
          name: [validateRules.required(['blur', 'change'])],
        }
      },
      roamGroupRules() {
        return {
          name: [validateRules.required(['blur', 'change'])],
        }
      },
      channelDataRules() {
        const defFreq = this.defaultFrequency
        const minFrequency = this.deviceWriteInfo.minFrequency
        const maxFrequency = this.deviceWriteInfo.maxFrequency
        const min = minFrequency ? bfutil.frequencyMhz2Hz(minFrequency) : defFreq.value
        const max = maxFrequency ? bfutil.frequencyMhz2Hz(maxFrequency) : defFreq.value + 80000000
        const smg = `${minFrequency || bfutil.frequencyHz2Mhz(defFreq.value) || 0}~${maxFrequency || bfutil.frequencyHz2Mhz(defFreq.value + 80000000)}`
        const frequency = [validateRules.required(['blur']), validateRules.range(['blur'], min, max, smg)]
        return {
          chName: [validateRules.required(['blur'])],
          receivingFrequency: frequency,
          transmittingFrequency: frequency,
        }
      },
      repeaterTimeSlotList() {
        return [
          {
            label: '1',
            value: 0,
          },
          {
            label: '2',
            value: 1,
          },
        ]
      },
      permissionConditionsList() {
        // 0 可用彩色码
        // 1 始终
        // 2 信道空闲
        return [
          {
            label: this.$t('dialog.availableColorCode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.always'),
            value: 1,
          },
          {
            label: this.$t('dialog.channelIdle'),
            value: 2,
          },
        ]
      },
      txPowerTypes() {
        // 0-低
        // 2-高
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.high'),
            value: 2,
          },
        ]
      },
      soundEncryptTypeList() {
        return [
          {
            value: 0,
            label: this.$t('dialog.unencrypted'),
          },
          {
            value: 1,
            label: this.$t('dialog.staticEncryption'),
          },
          {
            value: 2,
            label: this.$t('dialog.dynamicencryption'),
          },
          {
            value: 3,
            label: this.$t('dialog.advancedDynamicencryption'),
          },
        ]
      },
      onOffList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('dialog.on'),
            value: 1,
          },
        ]
      },
      rfidModeList() {
        return [
          {
            label: this.$t('dialog.autoCardReading'),
            value: 0,
          },
          {
            label: this.$t('dialog.triggerCardReading'),
            value: 1,
          },
        ]
      },
      rfidPowerList() {
        // 0 -18dbm
        // 1 -12dbm
        // 2 -6dbm
        // 3 0dbm
        return [
          {
            label: '-18dbm',
            value: 0,
          },
          {
            label: '-12dbm',
            value: 1,
          },
          {
            label: '-6dbm',
            value: 2,
          },
          {
            label: '0dbm',
            value: 3,
          },
        ]
      },
      rfidAnswerList() {
        // 0 数传指令应答
        // 1 采用芯片自动应答机制
        return [
          {
            label: this.$t('dialog.dataTrsCmdRes'),
            value: 0,
          },
          {
            label: this.$t('dialog.chipAutoResMechanism'),
            value: 1,
          },
        ]
      },

      // 虚拟时隙
      slotModeList() {
        return [
          {
            label: this.$t('dialog.timeSlot', { num: 1 }),
            value: 0,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 2 }),
            value: 1,
          },
          {
            label: this.$t('dialog.virtualCluster'),
            value: 2,
          },
        ]
      },
      virtualTimeSlotList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 1 }),
            value: 1,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 2 }),
            value: 2,
          },
        ]
      },
      localeList() {
        const locales = []
        if (SupportedLangList.includes(SupportedLang.zhCN)) {
          locales.push({
            label: this.$t('header.CN'),
            value: 0,
          })
        }
        if (SupportedLangList.includes(SupportedLang.enUS)) {
          locales.push({
            label: this.$t('header.EN'),
            value: 1,
          })
        }

        return locales
      },
      savePowerModeList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: '1:1',
            value: 1,
          },
          {
            label: '1:2',
            value: 2,
          },
          {
            label: '1:3',
            value: 3,
          },
          {
            label: '1:4',
            value: 4,
          },
        ]
      },
      freqDisplayList() {
        // 信道显示模式，0 频率显示，1 信道显示，2 频率+信道显示
        return [
          {
            label: this.$t('dialog.freqDisplay'),
            value: 0,
          },
          {
            label: this.$t('dialog.chDisplay'),
            value: 1,
          },
          {
            label: this.$t('dialog.freqAndChDisplay'),
            value: 2,
          },
        ]
      },
      allowCallInstructionList() {
        // 呼叫允许指示 ,000 无  001 模拟  010 模拟和数字 011 数字
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('dialog.analog'),
            value: 1,
          },
          {
            label: this.$t('dialog.analogAndDigital'),
            value: 2,
          },
          {
            label: this.$t('dialog.digital'),
            value: 3,
          },
        ]
      },
      recordCompressionRatioList() {
        // 录音压缩比 0 不压缩, 1 3.5倍  当录音使能==0时不可用
        return [
          {
            label: this.$t('dialog.nonCompacting'),
            value: 0,
          },
          {
            label: '3.5',
            value: 1,
          },
        ]
      },
      softKeyFuncDefine() {
        return Object.keys(ButtonKeys)
          .filter(key => {
            return this.$te(`writeFreq.softKeyFuncDefine.${key}`)
          })
          .map(key => {
            return {
              label: this.$t(`writeFreq.softKeyFuncDefine.${key}`),
              value: ButtonKeys[key],
            }
          })
      },
      SoftKeyCallType() {
        return SoftKeyCallType
      },
      buttonDefineAddressList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        const list = this.BP620DigitalAddress.filter(address => address.dmrId !== bfglob.fullCallDmrId).map(address => {
          return {
            label: address.name,
            value: address.id,
          }
        })
        return def.concat(list)
      },
      gpsModeList() {
        // 0:省电模式, 1:高性能模式
        return [
          {
            label: this.$t('writeFreq.off'),
            value: 0xff,
          },
          {
            label: this.$t('writeFreq.powerSavingMode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.highPerformanceMode'),
            value: 1,
          },
        ]
      },
      smsList() {
        return this.smsContent
      },
      selectDeviceData() {
        return bfglob.gdevices.getDataByIndex(this.selectedDeviceDmrId)
      },
      scanningGroupTransferTitles() {
        return [this.$t('writeFreq.availableChannel'), this.$t('writeFreq.containedChannel')]
      },
      // 没有设置默认发射组的信道集合
      noDefaultAddressChannelList() {
        return this.channelDataList.filter(channel => {
          // 过滤没有设置发射组的信道
          if (typeof channel.defaultAddress === 'undefined' || channel.defaultAddress === 0xffff) {
            return false
          }
          return true
        })
      },
      replyChannelList() {
        // 没有信道可选时，不可设置回复信道，值为无(0xFFFF)
        if (this.noDefaultAddressChannelList.length === 0) {
          return [
            {
              label: this.$t('dialog.nothing'),
              value: 0xffff,
            },
          ]
        }

        const channelList = this.noDefaultAddressChannelList.map(channel => {
          return {
            label: channel.chName,
            value: channel.chId,
          }
        })
        const def = [
          {
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
          },
        ]

        return def.concat(channelList)
      },
      specifiedTrChIDList() {
        return [
          {
            label: this.$t('writeFreq.lastActiveChannel'),
            value: 0xfffd,
          },
        ].concat(this.availableChannelList)
      },
      availableChannelList() {
        return [
          {
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
            disabled: true,
          },
        ].concat(
          this.channelDataList.map(channel => {
            return {
              label: channel.chName,
              value: channel.chId,
            }
          })
        )
      },
      roamGroupChannelList() {
        return [
          {
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
            disabled: true,
          },
        ].concat(
          this.channelDataList
            .filter(channel => channel.scanConfig.ipSiteConnect)
            .map(channel => {
              return {
                label: channel.chName,
                value: channel.chId,
              }
            })
        )
      },
      aloneWorkOptList() {
        return [
          {
            label: this.$t('writeFreq.button'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.voiceLaunch'),
            value: 1,
          },
        ]
      },
      triggerModeList() {
        return [
          {
            label: this.$t('writeFreq.onlyTilt'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.motionDetectionOnly'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.tiltOrMotionDetection'),
            value: 2,
          },
        ]
      },
      triggerTiltList() {
        return [
          {
            label: 60,
            value: 0,
          },
          {
            label: 45,
            value: 1,
          },
          {
            label: 30,
            value: 2,
          },
        ]
      },
      maxAloneWorkRemindTime() {
        const sce = this.alertConfig.responseTime * 60
        return sce > 0xff ? 0xff : sce
      },
      workAloneUnEnable() {
        return !this.alertConfig.aloneWorkEnable
      },
      upsideDownUnEnable() {
        return !this.alertConfig.upendEnable
      },
      reversePlayUnEnable() {
        return !this.alertConfig.upendEnable
      },
      disTriggerTilt() {
        return this.reversePlayUnEnable || this.alertConfig.upendConfig.triggerMode === 1
      },
      digitAlarmTypeList() {
        return [
          {
            label: this.$t('writeFreq.forbid'),
            value: 0,
          },
          {
            label: this.$t('dialog.common'),
            value: 1,
          },
          {
            label: this.$t('dialog.silent'),
            value: 2,
          },
          {
            label: this.$t('writeFreq.silenceCarryVoice'),
            value: 3,
          },
        ]
      },
      digitAlarmModeList() {
        return [
          {
            label: this.$t('dialog.emergency'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.emergencyAlarmAndCall'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.emergencyAlarmAndVoice'),
            value: 2,
          },
        ]
      },
      forbidAlarm() {
        return this.digitAlarm.type === 0
      },
      forbidReplyChannel() {
        const result = this.replyChannelList.filter(item => {
          return item.value === 0xffff
        })
        if (result.length > 0) {
          return true
        }

        return this.forbidAlarm
      },
      disMicActiveTime() {
        return this.digitAlarm.mode !== 2 || this.forbidAlarm
      },
      chTypeList() {
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0,
          },
          {
            label: this.$t('dialog.analogChannel'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.digitalCompatibleAnalog'),
            value: 2,
          },
          {
            label: this.$t('writeFreq.analogCompatibleDigital'),
            value: 3,
          },
        ]
      },
      chScanRoamGroupList() {
        const list = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
          ...this.scanList.map(item => {
            return {
              label: item.name,
              value: item.scanId,
            }
          }),
        ]
        if (this.oneChannel.chType === 0 && this.oneChannel.scanConfig.ipSiteConnect) {
          return list.concat(
            this.roamList.map(item => {
              return {
                label: item.name,
                value: (1 << 8) + item.roamId,
              }
            })
          )
        }

        return list
      },
      chTimeSlotCalList() {
        return [
          {
            label: this.$t('dataTable.fail'),
            value: 0,
          },
          {
            label: this.$t('dataTable.pass'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.firstChoice'),
            value: 2,
          },
        ]
      },
      bandwidthFlagList() {
        return [
          {
            label: this.$t('writeFreq.broadBand'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.narrowBand'),
            value: 1,
          },
        ]
      },
      emergencySysIdList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]
        const digitAlarmList = this.digitAlarmList.map(item => {
          return {
            label: item.name,
            value: item.id,
          }
        })
        return def.concat(digitAlarmList)
      },
      receiveGroupList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        const rxGroupList = this.rxGroupList.map(item => {
          return {
            label: item.groupName,
            value: item.groupId,
          }
        })
        return def.concat(rxGroupList)
      },
      defaultAddressList() {
        const cache = []
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ].concat(
          this.BP620DigitalAddress.filter(address => {
            if (cache.includes(address.id)) {
              return false
            }
            cache.push(address.id)
            return true
          }).map(address => {
            return {
              label: address.name,
              value: address.id,
            }
          })
        )
      },
      disableCtcssTxDps() {
        // 亚音码高字节的最高2位为数字亚音标识
        return this.onlyReceive || this.oneChannel.subsonicEncode === 0xffff || this.oneChannel.subsonicEncode >> 14 > 0
      },
      tailToneList() {
        return [
          {
            label: this.$t('writeFreq.noSubtone'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.standardPhase'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.nonstandardPhase'),
            value: 2,
          },
        ]
      },
      busyChannelLockList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.carrier'),
            value: 1,
          },
          {
            label: 'CTCSS/CDCSS',
            value: 2,
          },
        ]
      },
      getFixedDate() {
        const { year, month, day, hour, minute, second } = this.generalSettings

        let date = new Date()
        date = new Date(date.setSeconds(second))
        date = new Date(date.setMinutes(minute))
        date = new Date(date.setHours(hour))
        date = new Date(date.setDate(day))
        date = new Date(date.setMonth(month))
        date = new Date(date.setFullYear(year))

        return date
      },
      disAddDigital() {
        return this.digitAlarmList.length === this.digitAlarmLimit || !this.digitAlarm.name
      },
      disAddBookGroup() {
        return this.addressBookGroup.length === this.addressBookGroupLimit || !this.addressBookGroupItem.name
      },
      disAddScanGroup() {
        return this.scanList.length === this.scanGroupLimit || !this.scanGroup.name
      },
      disAddRoamGroup() {
        return this.roamList.length === this.roamGroupLimit || !this.roamGroup.name
      },
      deviceFuncConfig() {
        return this.deviceWriteInfo.config
      },
      isGConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.locate
      },
      isLConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.recording
      },
      isRConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.roam
      },
      isDConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.denoise
      },
      isWConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.workAlone
      },
      isBConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.bluetooth
      },
      onlyReceive() {
        return this.oneChannel.scanConfig.onlyReceive
      },
      isConnectNetworking() {
        return this.oneChannel.alertConfig.networking
      },
      addressBookCallTypes() {
        return CallType
      },
      addressBookTree() {
        return this.$refs[this.addrBookTreeId]
      },
      phoneBookTree() {
        return this.$refs[this.phoneBookTreeId]
      },
      receiveGroup() {
        return this.$refs[this.refReceiveGroup]
      },
      writeDataOption() {
        return [
          {
            type: 4,
            failedMsg: this.$t('msgbox.writeRegularSettingsFailed'),
          },
          {
            type: 5,
            failedMsg: this.$t('msgbox.writeDeySettingsFailed'),
          },
          {
            type: 6,
            failedMsg: this.$t('msgbox.writeSMSFailed'),
            option: { limit: 1 },
          },
          {
            type: 7,
            failedMsg: this.$t('msgbox.writeEncryptConfigFailed'),
          },
          {
            type: 8,
            failedMsg: this.$t('msgbox.writeEncryptKeyFailed'),
          },
          {
            type: 9,
            failedMsg: this.$t('msgbox.writeEncryptionARC4KeyFailed'),
          },
          {
            type: 10,
            // 3个密钥一个数据包，超出了320字节
            option: { limit: 2 },
            failedMsg: this.$t('msgbox.writeEncryptionAES256KeyFailed'),
          },
          {
            type: 13,
            failedMsg: this.$t('msgbox.writeGpsDataFailed'),
          },
          {
            type: 15,
            failedMsg: this.$t('msgbox.writeMenuFailed'),
          },
          {
            type: 16,
            failedMsg: this.$t('msgbox.writeSignalingSystemFailed'),
          },
          {
            type: 17,
            failedMsg: this.$t('msgbox.writeAlarmConfigFailed'),
          },
          {
            type: 19,
            failedMsg: this.$t('msgbox.writeDigitalAlarmFailed'),
          },
          {
            type: 21,
            failedMsg: this.$t('msgbox.writeAddressBookFailed'),
          },
          {
            type: 22,
            failedMsg: this.$t('msgbox.writeAddressBookGroupFailed'),
          },
          {
            type: 23,
            failedMsg: this.$t('msgbox.writePhoneBookFailed'),
          },
          {
            type: 24,
            failedMsg: this.$t('msgbox.writeReceivingGroupFailed'),
          },
          {
            type: 25,
            failedMsg: this.$t('msgbox.writeZoneDataFailed'),
          },
          {
            type: 26,
            failedMsg: this.$t('msgbox.writeChannelDataFailed'),
            option: { limit: 2 },
          },
          {
            type: 27,
            failedMsg: this.$t('msgbox.writeScanListFailed'),
          },
          {
            type: 28,
            failedMsg: this.$t('msgbox.writeRoamListFailed'),
          },
          {
            type: 51,
            failedMsg: this.$t('msgbox.writePatrolSystemConfigFailed'),
          },
          {
            type: 53,
            failedMsg: this.$t('msgbox.writeEmergencyAlarmConfigFailed'),
          },
          {
            type: 54,
            failedMsg: this.$t('msgbox.writeTraceMonitorConfigFailed'),
          },
          {
            type: 60,
            failedMsg: this.$t('msgbox.writeVirtualClusterFailed'),
          },
          {
            type: 61,
            option: {
              limit: 1,
            },
            failedMsg: this.$t('msgbox.writeSiteInfoFailed'),
          },
          // 最后写入编程密码
          {
            type: 3,
            failedMsg: this.$t('msgbox.writeProgrammingPwdFailed'),
          },
        ]
      },
      getClassInstance() {
        return getClassInstance
      },
      Model() {
        return this.expectedModel || Model
      },
      ipSiteConnect() {
        if (!this.oneChannel.scanConfig) {
          return false
        }
        return this.oneChannel.scanConfig.ipSiteConnect
      },
    },
    watch: {
      // 加密未启用时，禁用所有信道的加密配置
      encryptEnable(value) {
        if (value) return

        // 信道类型 0：数字 1：模拟 2：数字兼容模拟 3：模拟兼容数字
        const notEncryptChTypes = [1]
        for (let i = 0; i < this.channelDataList.length; i++) {
          if (notEncryptChTypes.includes(this.channelDataList[i].chType)) {
            continue
          }

          if (this.channelDataList[i].encryptConfig) {
            this.channelDataList[i].encryptConfig.enable = false
          }
        }
      },
      smsContent: {
        deep: true,
        handler(_data) {
          this.$nextTick(() => {
            // 短信内容变化时，检测按键定义中单键呼叫功能的设置
            this.detectButtonDefinedFromSmsChange()
          })
        },
      },
      noDefaultAddressChannelList(val) {
        if (val.length === 0) {
          this.digitAlarm.replyChannel = 0xffff
        } else if (val.length === 1) {
          // 可选的信道个数只有一个时，回复信道固定选中该信道
          this.digitAlarm.replyChannel = val[0].value
        }
      },
      'menuSettings.powerOnPwd'(val) {
        if (!val) {
          this.menuSettings.deviceConfig2.powerOnPassword = false
        }
      },
      // mixins计算属性
      selectDeviceData(data) {
        this.clearPrivateConfig()

        this.$nextTick(() => {
          if (data) {
            // 将选中的设备中关于信道等数据同步到界面中
            this.selectDeviceDataChanged(data)
          }
        })
      },
      'oneChannel.scanConfig.ipSiteConnect'(val) {
        this.oneChannel.scanListWrap = 0xff
        // IP站点连接未勾选，则扫描列表不可能选择漫游组和自动漫游配置
        if (!val) {
          this.oneChannel.scanConfig.autoRoam = false
        }
      },
      isConnectNetworking(val) {
        if (val) {
          this.oneChannel.scanConfig.autoScan = false
          this.oneChannel.scanConfig.autoRoam = false
          this.oneChannel.scanListWrap = 0xff
          if (this.oneChannel.alertConfig) {
            this.oneChannel.alertConfig.emergencyAlertTip = false
            this.oneChannel.alertConfig.emergencyAlertConfirm = false
            this.oneChannel.alertConfig.emergencyCallTip = false
          }
          this.oneChannel.emergencySysId = 0xff
        } else {
          if (this.oneChannel.alertConfig) {
            this.oneChannel.alertConfig.localCall = false
          }
        }
      },
    },
    components: {
      WriteFreqFooter,
      channelTransfer: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/channelTransfer.vue')),
      EmergencyAlarmConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/emergencyAlarmConfig.vue')),
      PatrolConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/patrolConfig.vue')),
      //bfInputNumber: defineAsyncComponent(() => import('@/components/common/bfInputNumber')),
      addressBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/addressBook')),
      phoneBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/phoneBook')),
      shortMessage: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/shortMessage')),
      receiveGroup: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/receiveGroup')),
      deviceInfo,

      frequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
      freqMapOffset: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/freqMapOffset')),
      TimeZone: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/TimeZoneV2.vue')),
      encryptSettings: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/encryptSettings')),
      SiteInfo_511svt: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/SiteInfo_511svt.vue')),
      addressBookGroup,
      recordListV2: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/recordListV2.vue')),
      bfInput,
      bfInputNumberV2,
      bfSelect,
      bfCheckbox,
      bfButton,
      bfTransfer,
    },
    mounted() {
      // 常规配置的语言选项，根据语言选择重置默认值
      if (this.generalSettings.locale !== undefined) {
        const localeList = this.localeList ?? []
        const localeOption = localeList.find(opt => opt.value === this.generalSettings.locale)
        this.generalSettings.locale = localeOption?.value ?? localeList[0]?.value ?? 0
      }
    },
    beforeMount() {
      this.initDigitalAlarm()
      this.initAddressBookGroup()
      this.initScanGroup()
      this.initRoamGroup()
      this.initSiteInfoList()

      bfglob.on('vdevices_table_update_data', this.updateDeviceData)
      bfglob.on('vdevices_table_delete_data', this.deleteDeviceData)
      bfglob.on('device_channel_changed', this.globalDeviceChannelsChanged)
      this.detectButtonDefinedFromSmsChange = debounce(this.detectButtonDefinedFromSmsChange, 1000)
    },
    beforeUnmount() {
      // Vue实例销毁前，取消订阅的一些方法主题
      bfglob.off('vdevices_table_update_data', this.updateDeviceData)
      bfglob.off('vdevices_table_delete_data', this.deleteDeviceData)
      bfglob.off('device_channel_changed', this.globalDeviceChannelsChanged)
    },
  }
</script>

<style lang="scss">
  @use '@/css/interphoneWf/tabsWf.scss' as *;
</style>
