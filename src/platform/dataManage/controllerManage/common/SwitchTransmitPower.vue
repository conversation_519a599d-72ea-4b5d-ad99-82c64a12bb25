<template>
  <el-form ref="serverSetting" :model="powerSet" label-position="top" class="w-[400px]">
    <el-form-item>
      <template #label>
        <EllipsisText :content="$t('dialog.txPower') + ':'" />
      </template>
      <bfSelect v-model="powerSet.txPower" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')" class="!w-full !h-[50px]">
        <el-option v-for="(item, i) in powerList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item v-if="showCustomPower">
      <template #label>
        <EllipsisText :content="$t('dialog.customPower') + ':'" />
      </template>
      <bfInput v-model="powerSet.curstomPowerLv" :disabled="disCustomPower" />
    </el-form-item>
    <el-form-item v-if="showCustomPower">
      <template #label>
        <EllipsisText :content="$t('dialog.customVehiclePlv') + ':'" />
      </template>
      <bfInput v-model="powerSet.customVehiclePlv" :disabled="disCustomPower" />
    </el-form-item>
    <div class="flex justify-center">
      <bfButton color-type="warning" :disabled="disabled" @click="queryRepeaterCurPowerSet" v-text="$t('dialog.writeIn')" />
    </div>
  </el-form>
</template>

<script>
  import repeaterWfMod, { DefModel, TR925Models } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'
  import { kcpPackageName } from '@/modules/protocol'
  import { cloneDeep, merge } from 'lodash'
  import bfSelect from '@/components/bfSelect/main'
  import bfInput from '@/components/bfInput/main'
  import bfButton from '@/components/bfButton/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'

  const PowerSet = {
    txPower: 0,
    curstomPowerLv: 0,
    customVehiclePlv: 0,
  }

  export default {
    name: 'SwitchTransmitPower',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: kcpPackageName,
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
      operation: {
        type: Number,
        default: 12,
      },
    },
    components: {
      bfButton,
      bfInput,
      bfSelect,
      EllipsisText,
    },
    data() {
      return {
        powerSet: cloneDeep(PowerSet),
      }
    },
    methods: {
      // 12:切换中继当前发射功率
      queryRepeaterCurPowerSet() {
        repeaterWfMod
          .writeInData(this.powerSet, this.defQueryOption)
          .then(res => {
            this.saveMethod('repeaterCurPowerSet', this.powerSet)
          })
          .catch(err => {
            bfglob.console.error('queryRepeaterCurPowerSet', err)
          })
      },
    },
    computed: {
      defQueryOption() {
        return {
          paraBin: {
            operation: this.operation,
          },
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          decodeMsgType: 'RepeaterCurPowerSet',
          packageName: this.packageName,
        }
      },
      showCustomPower() {
        // return TR925Models.includes(this.deviceModel)
        return false
      },
      disCustomPower() {
        return this.powerSet.txPower !== 3
      },

      // 功率选项
      tr925PowerTypes() {
        // TR925: 0=低功率 1=中功率 2=高功率 3自定义功率
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.mid'),
            value: 1,
          },
          {
            label: this.$t('dialog.high'),
            value: 2,
          },
          {
            label: this.$t('dialog.customize'),
            value: 3,
          },
        ]
      },
      tr805005PowerTypes() {
        // BF8100: 0=低功率 1=高功率
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.high'),
            value: 1,
          },
        ]
      },
      powerList() {
        if (TR925Models.includes(this.deviceModel.slice(0, 6))) {
          return this.tr925PowerTypes
        }
        return this.tr805005PowerTypes
      },
    },
    watch: {
      'powerSet.txPower'(val) {
        if (val !== 3) {
          this.powerSet.curstomPowerLv = 0
        }
      },
      'repeaterData.writeFrequencySetting.repeaterCurPowerSet': {
        immediate: true,
        deep: true,
        handler(val) {
          this.powerSet = merge({}, val || PowerSet)
        },
      },
    },
  }
</script>
