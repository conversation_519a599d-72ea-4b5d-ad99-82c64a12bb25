<template>
  <el-form ref="repeaterInfo" :model="repeaterInfo" label-position="top" class="w-[400px]">
    <el-form-item>
      <template #label>
        <EllipsisText :content="$t('dialog.model') + ':'" />
      </template>
      <bfInput v-model="modelName" readonly />
    </el-form-item>
    <el-form-item>
      <template #label>
        <EllipsisText :content="$t('dialog.version') + ':'" />
      </template>
      <bfInput v-model="repeaterInfo.version" readonly />
    </el-form-item>
    <el-form-item>
      <template #label>
        <EllipsisText :content="$t('dialog.lowFrequency') + ':'" />
      </template>
      <bfInput v-model="lowMhz" readonly />
    </el-form-item>
    <el-form-item>
      <template #label>
        <EllipsisText :content="$t('dialog.highFrequency') + ':'" />
      </template>
      <bfInput v-model="highMhz" readonly />
    </el-form-item>
    <el-form-item>
      <template #label>
        <EllipsisText :content="$t('dialog.serialNumber') + ':'" />
      </template>
      <bfInput v-model="repeaterInfo.sn" readonly />
    </el-form-item>
    <div class="flex justify-center gap-2" :class="{ 'only-query': onlyQuery }">
      <bfButton color-type="primary" :disabled="disabled" @click="queryRepeaterInfo" v-text="$t('dialog.queryRepeaterInfo')" />
    </div>
  </el-form>
</template>

<script>
  import { getModelName } from '@/writingFrequency/modelInfo'
  import bfutil from '@/utils/bfutil'
  import repeaterWfMod from '@/writingFrequency/repeater'
  import { kcpPackageName } from '@/modules/protocol'
  import { getRepeaterModelName } from '@/writingFrequency/customModelConfig'
  import { merge } from 'lodash'
  import bfInput from '@/components/bfInput/main.ts'
  import bfButton from '@/components/bfButton/main.ts'
  import EllipsisText from '@/components/common/EllipsisText.vue'

  const RepeaterInfo = {
    deviceModel: '',
    version: '',
    lowFrequency: 0,
    highFrequency: 0,
    sn: '',
  }

  export default {
    name: 'RepeaterInfo',
    components: {
      bfInput,
      bfButton,
      EllipsisText,
    },
    emits: ['update:modelValue'],
    props: {
      modelValue: {
        type: Object,
        default: () => {
          return {
            ...RepeaterInfo,
          }
        },
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
      onlyQuery: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        repeaterInfo: {
          ...RepeaterInfo,
        },
      }
    },
    computed: {
      defQueryOption() {
        return {
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          paraBin: {
            operation: 5,
            tableId: 1,
          },
          decodeMsgType: 'RepeaterInfo',
          packageName: kcpPackageName,
        }
      },
      lowMhz() {
        return this.repeaterInfo.lowFrequency === 0 ? '' : bfutil.frequencyHz2Mhz(this.repeaterInfo.lowFrequency)
      },
      highMhz() {
        return this.repeaterInfo.highFrequency === 0 ? '' : bfutil.frequencyHz2Mhz(this.repeaterInfo.highFrequency)
      },
      modelName() {
        return getRepeaterModelName(this.repeaterInfo.deviceModel) || getModelName(this.repeaterInfo.deviceModel)
      },
    },
    methods: {
      queryRepeaterInfo() {
        repeaterWfMod
          .queryConfig(this.defQueryOption)
          .then(res => {
            res.sn = repeaterWfMod.snDecode(res.sn)
            this.repeaterInfo = merge(this.repeaterInfo, res)
            this.saveMethod('repeaterInfo', this.repeaterInfo)
            this.$emit('update:modelValue', this.repeaterInfo)
          })
          .catch(err => {
            bfglob.console.error('queryRepeaterInfo', err)
          })
      },
    },
    watch: {
      modelValue: {
        immediate: true,
        deep: true,
        handler(val) {
          this.repeaterInfo = merge(this.repeaterInfo, val)
        },
      },
      'repeaterData.writeFrequencySetting.repeaterInfo': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.repeaterInfo = merge(this.repeaterInfo, val)
          }
        },
      },
    },
  }
</script>
