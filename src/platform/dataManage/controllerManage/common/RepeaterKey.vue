<template>
  <el-form ref="buttonDefinition" :model="buttonDefinition" label-position="top" class="write-frequency-form">
    <!--长按持续时间-->
    <el-form-item prop="longPressTime" class="longPressTime">
      <template #label>
        <EllipsisText :content="$t('dialog.longPressDuration') + ':'" />
      </template>
      <bfInputNumberV2 v-model="buttonDefinition.longPressTime" :min="1000" :max="5000" :step="200" class="!w-full" />
    </el-form-item>
    <!--按键1-4功能-->
    <el-form-item prop="key01ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key01', shortPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_01ShortPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :disabled="isTR925Model"
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item prop="key01LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key01', longPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_01LongPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item prop="key02ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key02', shortPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_02ShortPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :disabled="isTR925Model"
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item prop="key02LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key02', longPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_02LongPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>

    <el-form-item prop="key03ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key03', shortPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_03ShortPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :disabled="isTR925Model"
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item prop="key03LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key03', longPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_03LongPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>

    <el-form-item prop="key04ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key04', shortPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_04ShortPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :disabled="isTR925Model"
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item prop="key04LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.key04', longPress) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.key_04LongPressFunc"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <!--数字键0-9功能-->
    <el-form-item v-if="isTR925Model" prop="key01ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 0 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num00"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item v-if="isTR925Model" prop="key01LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 1 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num01"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>

    <el-form-item v-if="isTR925Model" prop="key02ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 2 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num02"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item v-if="isTR925Model" prop="key02LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 3 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num03"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>

    <el-form-item v-if="isTR925Model" prop="key03ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 4 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num04"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item v-if="isTR925Model" prop="key03LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 5 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num05"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>

    <el-form-item v-if="isTR925Model" prop="key04ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 6 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num06"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item v-if="isTR925Model" prop="key04LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 7 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num07"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>

    <el-form-item v-if="isTR925Model" prop="key01ShortPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 8 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num08"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>

    <el-form-item v-if="isTR925Model" prop="key01LongPressFunc">
      <template #label>
        <EllipsisText :content="$t('dialog.numKey', { num: 9 }) + ':'" />
      </template>
      <bfSelect
        v-model="buttonDefinition.num09"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        class="!w-full !h-[50px]"
      >
        <el-option v-for="(item, i) in buttonFuncList" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
  </el-form>

  <div class="flex justify-center gap-2 actions">
    <bfButton color-type="primary" :disabled="disabled" @click="queryButtonDefinition" v-text="$t('dialog.querySetting')" />
    <bfButton color-type="warning" :disabled="disabled" @click="writeInButtonDefinition" v-text="$t('dialog.writeIn')" />
  </div>
</template>

<script>
  import bfproto, { kcpPackageName } from '@/modules/protocol'
  import bfutil from '@/utils/bfutil'
  import repeaterWfMod, { DefModel, TR925Models } from '@/writingFrequency/repeater'
  import { merge } from 'lodash'
  import bfInputNumberV2 from '@/components/bfInputNumber/main.ts'
  import bfSelect from '@/components/bfSelect/main.ts'
  import bfButton from '@/components/bfButton/main.ts'
  import EllipsisText from '@/components/common/EllipsisText.vue'

  const RepeaterKeyEnum = bfproto.lookupEnum('RepeaterKey') || {}
  const ButtonDefinition = {
    longPressTime: 1000,
    key_01ShortPressFunc: 1,
    key_01LongPressFunc: 1,
    key_02ShortPressFunc: 2,
    key_02LongPressFunc: 2,
    key_03ShortPressFunc: 3,
    key_03LongPressFunc: 3,
    key_04ShortPressFunc: 0,
    key_04LongPressFunc: 0,
  }
  const TR925_ButtonDefinition = {
    ...ButtonDefinition,

    key_01ShortPressFunc: RepeaterKeyEnum.VolumeDown,
    key_02ShortPressFunc: RepeaterKeyEnum.VolumeUp,
    key_03ShortPressFunc: RepeaterKeyEnum.ChannelDown,
    key_04ShortPressFunc: RepeaterKeyEnum.ChannelUp,

    key_01LongPressFunc: RepeaterKeyEnum.AreaDown,
    key_02LongPressFunc: RepeaterKeyEnum.AreaUp,
    key_03LongPressFunc: RepeaterKeyEnum.AddressBook,
    key_04LongPressFunc: RepeaterKeyEnum.PreMadeSms,

    // 数字键
    num_key_long_press_func: [],
    num00: RepeaterKeyEnum.None,
    num01: RepeaterKeyEnum.None,
    num02: RepeaterKeyEnum.None,
    num03: RepeaterKeyEnum.None,
    num04: RepeaterKeyEnum.None,
    num05: RepeaterKeyEnum.None,
    num06: RepeaterKeyEnum.None,
    num07: RepeaterKeyEnum.None,
    num08: RepeaterKeyEnum.None,
    num09: RepeaterKeyEnum.None,
  }

  export default {
    name: 'RepeaterKey',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: '',
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
    },
    data() {
      return {
        buttonDefinition: {
          ...ButtonDefinition,
        },
      }
    },
    methods: {
      saveConfig(data) {
        this.buttonDefinition = merge(this.buttonDefinition, data)

        this.saveMethod('buttonDefinition', data)
      },
      // 按键功能设置
      queryButtonDefinition() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            tableId: 3,
          },
        })

        repeaterWfMod
          .queryConfig(options)
          .then(res => {
            return this.saveConfig(res)
          })
          .catch(err => {
            bfglob.console.error('queryButtonDefinition', err)
          })
      },
      writeInButtonDefinition() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 6,
            tableId: 3,
          },
        })

        repeaterWfMod
          .writeInData(this.buttonDefinition, options)
          .then(res => {
            return this.saveConfig(this.buttonDefinition)
          })
          .catch(err => {
            bfglob.console.error('writeInButtonDefinition', err)
          })
      },
    },
    computed: {
      defQueryOption() {
        return {
          paraBin: {
            operation: 5,
          },
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          decodeMsgType: 'RepeaterKeyFunctionSetting',
          packageName: this.packageName || kcpPackageName,
        }
      },
      isTR925Model() {
        return TR925Models.includes(this.deviceModel.slice(0, 6))
      },
      shortPress() {
        return { type: this.$t('dialog.shortPress') }
      },
      longPress() {
        return { type: this.$t('dialog.longPress') }
      },
      buttonPressFunctionList() {
        // 定义类型	      值
        // 无功能	        0
        // 信道向上切换	    1
        // 信道向下切换	    2
        // 高低功率切换	    3
        // 恢复默认IP地址  	4
        // 恢复默认IP地址  	5
        return [
          {
            label: this.$t('dialog.noFunction'),
            value: 0,
          },
          {
            label: this.$t('dialog.chUpSwitch'),
            value: 1,
          },
          {
            label: this.$t('dialog.chDownSwitch'),
            value: 2,
          },
          {
            label: this.$t('dialog.highAndLowPowerSwitch'),
            value: 3,
          },
          // {
          //   label: this.$t('dialog.restoreDefaultIPAddress'),
          //   value: 4
          // }
        ]
      },
      tr925ModelFuncList() {
        return [
          {
            label: this.$t('dialog.noFunction'),
            value: this.RepeaterKeyEnum.None,
          },
          {
            label: this.$t('dialog.volumeUp'),
            value: this.RepeaterKeyEnum.VolumeUp,
          },
          {
            label: this.$t('dialog.volumeDown'),
            value: this.RepeaterKeyEnum.VolumeDown,
          },
          {
            label: this.$t('dialog.channelUp'),
            value: this.RepeaterKeyEnum.ChannelUp,
          },
          {
            label: this.$t('dialog.channelDown'),
            value: this.RepeaterKeyEnum.ChannelDown,
          },
          {
            label: this.$t('dialog.channelSetting'),
            value: this.RepeaterKeyEnum.ChannelSetting,
          },
          {
            label: this.$t('dialog.powerSwitch'),
            value: this.RepeaterKeyEnum.PowerSwitch,
          },
          {
            label: this.$t('dialog.monitorSwitch'),
            value: this.RepeaterKeyEnum.MonitorSwitch,
          },
          {
            label: this.$t('dialog.areaUp'),
            value: this.RepeaterKeyEnum.AreaUp,
          },
          {
            label: this.$t('dialog.areaDown'),
            value: this.RepeaterKeyEnum.AreaDown,
          },
          {
            label: this.$t('dialog.squelchSwitch'),
            value: this.RepeaterKeyEnum.SquelchSwitch,
          },
          {
            label: this.$t('dialog.addressBook'),
            value: this.RepeaterKeyEnum.AddressBook,
          },
          {
            label: this.$t('dialog.preMadeSms'),
            value: this.RepeaterKeyEnum.PreMadeSms,
          },
          {
            label: this.$t('dialog.callRecord'),
            value: this.RepeaterKeyEnum.CallRecord,
          },
          {
            label: this.$t('dialog.rootDirList'),
            value: this.RepeaterKeyEnum.RootDirList,
          },
          {
            label: this.$t('dialog.standbyInterface'),
            value: this.RepeaterKeyEnum.StandbyInterface,
          },
          {
            label: this.$t('dialog.errorTestFire'),
            value: this.RepeaterKeyEnum.ErrorTestFire,
          },
          {
            label: this.$t('dialog.errorTestReceive'),
            value: this.RepeaterKeyEnum.ErrorTestReceive,
          },
        ]
      },
      buttonFuncList() {
        if (this.isTR925Model) {
          return this.tr925ModelFuncList
        }
        return this.buttonPressFunctionList
      },
      RepeaterKeyEnum() {
        if (this.isTR925Model) {
          return RepeaterKeyEnum
        }

        return {}
      },
    },
    watch: {
      'repeaterData.writeFrequencySetting.buttonDefinition': {
        immediate: true,
        deep: true,
        handler(val) {
          let buttonDefinition = ButtonDefinition
          if (this.isTR925Model) {
            buttonDefinition = TR925_ButtonDefinition
          }

          this.buttonDefinition = merge({}, val || buttonDefinition)
        },
      },
    },
    beforeMount() {
      // 根据型号初始化按键功能参数
      if (this.isTR925Model) {
        this.buttonDefinition = merge(this.buttonDefinition, TR925_ButtonDefinition)
      }
    },
    components: {
      bfInputNumberV2,
      bfSelect,
      bfButton,
      EllipsisText,
    },
  }
</script>
