1. 将组件中的子组件`<selectDevice>`删除， 并添加：props: {
   selectedDeviceDmrId: {
   type: String,
   default: '',
   },
   isReading: {
   type: Boolean,
   default: false,
   },
   isWriting: {
   type: Boolean,
   default: false,
   },
   },
   emits: ['update:selectedDeviceDmrId', 'update:isReading', 'update:isWriting'],
   在代码中检查有修改这三个参数的地方， 使用emits来发布修改事件；
2. 将组件中最外层div的`class="writer-frequency-wrap"`删除；将第一个el-tab且存在`writer-frequency-tabs`的class替换为class`interphone-tab`， 一般在第3行；
3. 将代码中中使用el-input的地方替换为使用src/components/bfInput/main.ts;
4. 将代码中使用el-input-number的地方替换为使用src/components/bfInputNumber/main.ts, 导入src/components/bfInputNumber/main.ts时应该将名称命名为bfInputNumberV2来使用, 且添加class="!w-full";
5. 将代码中使用el-select的地方替换为使用src/components/bfSelect/main.ts, 并添加class="!w-full !h-[50px]";
6. 将代码中使用el-checkbox的地方替换为使用src/components/bfCheckbox/main.ts;
7. 将代码中使用el-Radio的地方替换为使用src/components/bfRadio/main.ts;
8. 将代码中使用el-button的地方替换为使用src/components/bfButton/main.ts, 如果el-button上使用了class需要删除, 将type名称替换为color-type;
9. 将代码中使用el-transfer的地方替换为使用src/components/bfTransfer/main.ts;
10. 如果有el-form, 将表单labelPosition设置为`top`;
11. 如果有el-form-item使用, 将label+":";
12. 如果有使用el-table, 则在el-table上添加class="bf-table";
13. 如果有使用el-tooltip或者el-popover组件, 则将popper-class属性添加"bf-tooltip";
14. 如果有使用el-dialog, 则将el-dialog替换为使用'src/components/bfDialog/main';
15. 如果有使用el-transfer， 则将el-transfer替换为使用'src/components/bfTransfer/main';
16. 如果有使用el-date-picker组件， 在其class上添加`bf-range-date-picker`;
17. 如果有使用el-col, 则将上面的xs和sm属性删除，只有xs或sm也需要删除, 设置:span='8';
18. 如果有使用el-tabs组件， 在其class上添加`bf-tab`；
19. 设置el-row的class添加"!w-[calc(100%_-_20px)]";
20. 对组件中的`<!--警报-->`、`<!--通讯录群组-->`、`<!--扫描-->`、`<!--漫游-->`、`<!-- 站点信息 `的el-tab-pane， 则参考src/platform/dataManage/deviceManage/views/BP610.vue中的`第2293-2550`行代码来。注意：`<!--漫游-->和<!--扫描-->`class为class="basis-5/6"的标签里面的el-form的class需要设置为class="grid grid-cols-2 gap-x-3"和将`<bf-transfer`所在的el-form-item的class中添加`col-span-2 justify-self-center`。 `<!--通讯录群组-->`;中的`w-[122px]`设置为`w-[142px]`；
21. 对组件中的`<!--按键设置-->`中的`<el-table>`里面的`<el-form-item>`添`!m-0`的class；
22. 如果有使用@/components/common/generateDmrId, 将其替换为使用src/components/common/DmridInput.vue;
23. `第2-8点`和`第13,第14，第16点` 可以先导入组件后， 在components注册后，通过脚本或其他方式去一次性替换；
24. 检查`1-21`点，每一点都当作一个`task`来生成， 执行当前task的时候， 检查组件中是否存在task的需要替换的目标， 没有则标记该task已完成。
