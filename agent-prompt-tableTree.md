1. 将组件中的子组件`<selectDevice>`删除， 并添加：props: {
   selectedDeviceDmrId: {
   type: String,
   default: '',
   },
   isReading: {
   type: Boolean,
   default: false,
   },
   isWriting: {
   type: Boolean,
   default: false,
   },
   },
   emits: ['update:selectedDeviceDmrId', 'update:isReading', 'update:isWriting'],
   在代码中检查有修改这三个参数的地方， 使用emits来发布修改事件；
2. 将代码中中使用el-input的地方替换为使用src/components/bfInput/main.ts;
3. 将代码中使用el-input-number的地方替换为使用src/components/bfInputNumber/main.ts, 导入src/components/bfInputNumber/main.ts时应该将名称命名为bfInputNumberV2来使用, 且添加class="!w-full";
4. 将代码中使用el-select的地方替换为使用src/components/bfSelect/main.ts, 并添加class="!w-full !h-[50px]";
5. 将代码中使用el-checkbox的地方替换为使用src/components/bfCheckbox/main.ts;
6. 将代码中使用el-Radio的地方替换为使用src/components/bfRadio/main.ts;
7. 将代码中使用el-button的地方替换为使用src/components/bfButton/main.ts, 如果el-button上使用了class需要删除, 将type名称替换为color-type;
8. 将代码中使用el-transfer的地方替换为使用src/components/bfTransfer/main.ts;
9. 如果有el-form, 将表单labelPosition设置为`top`;
10. 如果有el-form-item使用, 将label+":";
11. 如果有使用el-table, 则在el-table上添加class="bf-table";
12. 如果有使用el-tooltip或者el-popover组件, 则将popper-class属性添加"bf-tooltip";
13. 如果有使用el-dialog, 则将el-dialog替换为使用'src/components/bfDialog/main';
14. 如果有使用el-transfer， 则将el-transfer替换为使用'src/components/bfTransfer/main';
15. 如果有使用el-date-picker组件， 在其class上添加`bf-range-date-picker`;
16. 如果有使用el-col, 则将上面的xs和sm属性删除，只有xs或sm也需要删除, 设置:span='8';
17. 如果有使用el-tabs组件， 在其class上添加`bf-tab`；
18. 设置el-row的class添加"!w-[calc(100%_-_20px)]";
